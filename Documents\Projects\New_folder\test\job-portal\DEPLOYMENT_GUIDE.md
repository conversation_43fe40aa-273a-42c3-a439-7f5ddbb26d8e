# 🚀 Job Portal Deployment Guide

This guide will help you deploy the Job Portal application to production using Vercel (frontend) and Railway (backend).

## 📋 Prerequisites

- Node.js 18+ installed
- Git repository (GitHub, GitLab, or Bitbucket)
- MongoDB Atlas account (free tier available)
- Vercel account (free tier available)
- Railway account (free tier available)

## 🗄️ Step 1: Set Up MongoDB Atlas

1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free account and cluster
3. Create a database user with read/write permissions
4. Whitelist your IP address (or use 0.0.0.0/0 for all IPs)
5. Get your connection string (replace `<password>` with your actual password)

```
mongodb+srv://username:<password>@cluster0.xxxxx.mongodb.net/jobportal?retryWrites=true&w=majority
```

## 🖥️ Step 2: Deploy Backend to Railway

### Option A: Using Railway CLI (Recommended)

1. Install Railway CLI:
```bash
npm install -g @railway/cli
```

2. Login to Railway:
```bash
railway login
```

3. Navigate to backend directory:
```bash
cd backend
```

4. Initialize Railway project:
```bash
railway init
```

5. Set environment variables:
```bash
railway variables set MONGODB_URI="your-mongodb-connection-string"
railway variables set JWT_SECRET="your-super-secret-jwt-key-here"
railway variables set NODE_ENV="production"
railway variables set PORT="5000"

# Optional: External API keys
railway variables set RAPIDAPI_KEY="your-rapidapi-key"
railway variables set ADZUNA_APP_ID="your-adzuna-app-id"
railway variables set ADZUNA_APP_KEY="your-adzuna-app-key"
```

6. Deploy:
```bash
railway up
```

### Option B: Using Railway Dashboard

1. Go to [Railway](https://railway.app)
2. Connect your GitHub repository
3. Select the backend folder
4. Set environment variables in the dashboard
5. Deploy automatically

## 🌐 Step 3: Deploy Frontend to Vercel

### Option A: Using Vercel CLI (Recommended)

1. Install Vercel CLI:
```bash
npm install -g vercel
```

2. Navigate to project root:
```bash
cd job-portal
```

3. Deploy:
```bash
vercel
```

4. Follow the prompts:
   - Link to existing project? **N**
   - Project name: **job-portal**
   - Directory: **frontend**
   - Build command: **npm run build**
   - Output directory: **dist**

5. Set environment variables in Vercel dashboard:
   - `VITE_API_URL`: Your Railway backend URL (e.g., `https://your-app.railway.app/api`)

### Option B: Using Vercel Dashboard

1. Go to [Vercel](https://vercel.com)
2. Import your GitHub repository
3. Set root directory to `frontend`
4. Set environment variables:
   - `VITE_API_URL`: Your Railway backend URL

## 🔧 Step 4: Configure Environment Variables

### Backend Environment Variables (Railway)

```bash
MONGODB_URI=mongodb+srv://username:<EMAIL>/jobportal
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
NODE_ENV=production
PORT=5000

# Optional: External Job APIs
RAPIDAPI_KEY=your-rapidapi-key-for-jsearch
ADZUNA_APP_ID=your-adzuna-application-id
ADZUNA_APP_KEY=your-adzuna-application-key
```

### Frontend Environment Variables (Vercel)

```bash
VITE_API_URL=https://your-backend-url.railway.app/api
VITE_APP_NAME=Job Portal
VITE_ENABLE_EXTERNAL_JOBS=true
```

## 🧪 Step 5: Test Your Deployment

1. **Backend Health Check**: Visit `https://your-backend-url.railway.app/`
2. **API Test**: Visit `https://your-backend-url.railway.app/api/jobs`
3. **Frontend**: Visit your Vercel URL
4. **Full Flow**: Register, login, post a job, apply for jobs

## 🔒 Step 6: Security Checklist

- [ ] MongoDB Atlas IP whitelist configured
- [ ] Strong JWT secret (32+ characters)
- [ ] CORS configured for your frontend domain
- [ ] Environment variables set (not hardcoded)
- [ ] API keys secured and not exposed in frontend
- [ ] HTTPS enabled (automatic with Vercel/Railway)

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure backend CORS is configured for your frontend domain
   - Check that API URL is correct in frontend environment variables

2. **Database Connection Issues**
   - Verify MongoDB Atlas connection string
   - Check IP whitelist settings
   - Ensure database user has proper permissions

3. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are in package.json
   - Check build logs for specific errors

4. **API Not Working**
   - Verify backend is deployed and running
   - Check environment variables are set correctly
   - Test API endpoints directly

## 🎉 Success!

Your Job Portal should now be live! 

- **Frontend**: `https://your-app.vercel.app`
- **Backend**: `https://your-app.railway.app`

## 📝 Next Steps

1. Set up custom domain (optional)
2. Configure email notifications
3. Set up monitoring and alerts
4. Implement analytics
5. Add more external job providers
6. Set up CI/CD pipeline
7. Add automated testing

## 🔄 Updates and Maintenance

To update your deployment:

1. **Backend**: Push changes to your repository, Railway will auto-deploy
2. **Frontend**: Push changes to your repository, Vercel will auto-deploy

For manual deployments:
- Backend: `railway up` in backend directory
- Frontend: `vercel --prod` in project root
