const mongoose = require('mongoose');

const assignmentSubmissionSchema = new mongoose.Schema({
    assignmentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Assignment',
        required: true
    },
    applicationId: { // Link to the specific job application
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Application',
        required: true,
        unique: true // One submission per application
    },
    candidateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // User with role 'candidate'
        required: true
    },
    jobId: { // Denormalized from Assignment for easier querying
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Job',
        required: true
    },
    submissionContent: { // For text-based submissions
        type: String,
        trim: true
    },
    submissionFileUrl: { // URL to S3/Cloudinary for submitted file (e.g., zip, pdf, code)
        type: String,
        trim: true,
        match: [/^(ftp|http|https):\/\/[^ "]+$/, 'Please provide a valid URL for the submission file']
    },
    submittedDate: {
        type: Date,
        default: Date.now
    },
    status: {
        type: String,
        enum: ['Submitted', 'Under Review', 'Graded', 'Needs Revision'],
        default: 'Submitted'
    },
    grade: { // e.g., a score, pass/fail, or a more detailed rubric object
        type: String, // Could be a number or a string like 'Pass'
        trim: true
    },
    feedback: { // Feedback from the employer
        type: String,
        trim: true
    },
    gradedDate: {
        type: Date
    },
    gradedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User' // Employer who graded
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Middleware to update `updatedAt` field on save
assignmentSubmissionSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

assignmentSubmissionSchema.index({ assignmentId: 1 });
assignmentSubmissionSchema.index({ applicationId: 1 });
assignmentSubmissionSchema.index({ candidateId: 1 });
assignmentSubmissionSchema.index({ jobId: 1 });
assignmentSubmissionSchema.index({ status: 1 });

module.exports = mongoose.model('AssignmentSubmission', assignmentSubmissionSchema);