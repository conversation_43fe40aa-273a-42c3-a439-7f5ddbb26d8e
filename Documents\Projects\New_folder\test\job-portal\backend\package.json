{"name": "job-portal-backend", "version": "1.0.0", "description": "Backend for the Job Portal System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0"}, "devDependencies": {"nodemon": "^2.0.20"}, "engines": {"node": ">=14.0.0"}}