# 🚀 Deployment Guide

This guide covers deploying the Job Portal application to production environments.

## 📋 Production Readiness Checklist

### ✅ Security
- [ ] Environment variables configured securely
- [ ] JWT secrets are strong and unique
- [ ] Database credentials secured
- [ ] CORS configured for production domains only
- [ ] Rate limiting implemented
- [ ] Input validation on all endpoints
- [ ] SQL injection protection (using Mongoose)
- [ ] XSS protection headers
- [ ] HTTPS enforced

### ✅ Performance
- [ ] Database indexes optimized
- [ ] API response caching implemented
- [ ] Frontend build optimized
- [ ] Image optimization
- [ ] CDN configured for static assets
- [ ] Gzip compression enabled
- [ ] Database connection pooling

### ✅ Monitoring & Logging
- [ ] Error logging configured
- [ ] Performance monitoring setup
- [ ] Uptime monitoring
- [ ] Database monitoring
- [ ] User analytics (optional)
- [ ] Error tracking (Sentry recommended)

### ✅ Backup & Recovery
- [ ] Database backup strategy
- [ ] File backup strategy
- [ ] Disaster recovery plan
- [ ] Data retention policies

## 🌐 Frontend Deployment (Vercel)

### Prerequisites
- Vercel account
- GitHub repository
- Backend deployed and accessible

### Step 1: Prepare Frontend for Deployment

1. **Update environment variables**
```bash
# frontend/.env.production
VITE_API_URL=https://your-backend-domain.com/api
VITE_APP_NAME=Job Portal
VITE_ENABLE_EXTERNAL_JOBS=true
```

2. **Build and test locally**
```bash
cd frontend
npm run build
npm run preview
```

3. **Update Vercel configuration**
```json
{
  "version": 2,
  "name": "job-portal",
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### Step 2: Deploy to Vercel

1. **Using Vercel CLI**
```bash
npm i -g vercel
cd frontend
vercel --prod
```

2. **Using GitHub Integration**
- Connect your GitHub repository to Vercel
- Configure build settings:
  - Build Command: `npm run build`
  - Output Directory: `dist`
  - Install Command: `npm install`

3. **Configure Environment Variables**
In Vercel dashboard, add:
- `VITE_API_URL`: Your backend API URL
- `VITE_APP_NAME`: Job Portal
- `VITE_ENABLE_EXTERNAL_JOBS`: true

## 🖥️ Backend Deployment Options

### Option 1: Railway

1. **Create Railway account**
2. **Connect GitHub repository**
3. **Configure environment variables**
4. **Deploy with one click**

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway link
railway up
```

### Option 2: Heroku

1. **Create Heroku app**
```bash
heroku create your-job-portal-api
```

2. **Configure environment variables**
```bash
heroku config:set JWT_SECRET=your_jwt_secret
heroku config:set MONGO_URI=your_mongodb_atlas_uri
heroku config:set NODE_ENV=production
heroku config:set RAPIDAPI_KEY=your_rapidapi_key
```

3. **Deploy**
```bash
git subtree push --prefix backend heroku main
```

### Option 3: DigitalOcean App Platform

1. **Create new app**
2. **Connect GitHub repository**
3. **Configure build settings**:
   - Source Directory: `/backend`
   - Build Command: `npm install`
   - Run Command: `npm start`

## 🗄️ Database Setup (MongoDB Atlas)

### Step 1: Create MongoDB Atlas Cluster

1. **Sign up at MongoDB Atlas**
2. **Create a new cluster**
3. **Configure network access**
   - Add your application's IP addresses
   - For development: Add 0.0.0.0/0 (not recommended for production)

### Step 2: Create Database User

1. **Go to Database Access**
2. **Add new database user**
3. **Set username and password**
4. **Grant read/write access**

### Step 3: Get Connection String

1. **Click "Connect" on your cluster**
2. **Choose "Connect your application"**
3. **Copy the connection string**
4. **Replace `<password>` with your database user password**

### Step 4: Configure Application

```env
MONGO_URI=mongodb+srv://username:<EMAIL>/job_portal?retryWrites=true&w=majority
```

## 🔧 Environment Configuration

### Backend Environment Variables

```env
# Production Backend .env
NODE_ENV=production
PORT=5000
JWT_SECRET=your_super_secure_jwt_secret_here
MONGO_URI=mongodb+srv://user:<EMAIL>/job_portal

# External APIs
RAPIDAPI_KEY=your_rapidapi_key
ADZUNA_APP_ID=your_adzuna_app_id
ADZUNA_APP_KEY=your_adzuna_app_key

# Security
CORS_ORIGIN=https://your-frontend-domain.vercel.app

# Performance
EXTERNAL_JOBS_CACHE_DURATION=1800000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Frontend Environment Variables

```env
# Production Frontend .env
VITE_API_URL=https://your-backend-domain.com/api
VITE_APP_NAME=Job Portal
VITE_ENABLE_EXTERNAL_JOBS=true
VITE_ENABLE_ANALYTICS=true
```

## 🔒 Security Best Practices

### 1. Environment Variables
- Never commit `.env` files
- Use different secrets for each environment
- Rotate secrets regularly

### 2. CORS Configuration
```javascript
// Update backend/server.js
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.vercel.app'] 
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
};
app.use(cors(corsOptions));
```

### 3. Rate Limiting
```javascript
// Add to backend/server.js
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use('/api/', limiter);
```

## 📊 Monitoring Setup

### 1. Error Tracking (Sentry)

```bash
npm install @sentry/node @sentry/react
```

**Backend:**
```javascript
const Sentry = require('@sentry/node');
Sentry.init({ dsn: process.env.SENTRY_DSN });
```

**Frontend:**
```javascript
import * as Sentry from '@sentry/react';
Sentry.init({ dsn: process.env.VITE_SENTRY_DSN });
```

### 2. Performance Monitoring

- Use MongoDB Atlas monitoring
- Set up Vercel Analytics
- Configure uptime monitoring (UptimeRobot)

## 🚀 Deployment Commands

### Quick Deployment Script

```bash
#!/bin/bash
# deploy.sh

echo "🚀 Deploying Job Portal..."

# Build frontend
echo "📦 Building frontend..."
cd frontend
npm run build

# Deploy frontend to Vercel
echo "🌐 Deploying frontend..."
vercel --prod

# Deploy backend (example for Railway)
echo "🖥️ Deploying backend..."
cd ../backend
railway up

echo "✅ Deployment complete!"
```

## 🔄 CI/CD Pipeline (GitHub Actions)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install and Build
        run: |
          cd frontend
          npm install
          npm run build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          working-directory: ./frontend

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Railway
        uses: bervProject/railway-deploy@v1.0.0
        with:
          railway_token: ${{ secrets.RAILWAY_TOKEN }}
          service: ${{ secrets.RAILWAY_SERVICE }}
```

## 🆘 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check CORS_ORIGIN environment variable
   - Verify frontend domain in CORS configuration

2. **Database Connection Issues**
   - Verify MongoDB Atlas IP whitelist
   - Check connection string format
   - Ensure database user has correct permissions

3. **Environment Variables Not Loading**
   - Check variable names (case-sensitive)
   - Verify deployment platform configuration
   - Restart application after changes

4. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Review build logs for specific errors

### Support

- Check deployment platform documentation
- Review application logs
- Test API endpoints individually
- Verify database connectivity

---

**🎉 Your Job Portal is now ready for production!**
