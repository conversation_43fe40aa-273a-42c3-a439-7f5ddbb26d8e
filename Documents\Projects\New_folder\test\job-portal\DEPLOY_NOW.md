# 🚀 DEPLOY NOW - Step by Step Instructions

## 📋 **STEP 1: Push Code to GitHub (5 minutes)**

### 1.1 Initialize Git Repository
Open Command Prompt/PowerShell in your project folder and run:

```bash
cd "Documents\Projects\New_folder\test\job-portal"
git init
git add .
git commit -m "Initial commit - Job Portal ready for deployment"
```

### 1.2 Create GitHub Repository
1. Go to [GitHub.com](https://github.com)
2. Click "New repository"
3. Name: `job-portal`
4. Description: `Full-stack job portal with React frontend and Node.js backend`
5. Keep it **Public** (for free deployments)
6. **Don't** initialize with README (we already have one)
7. Click "Create repository"

### 1.3 Push to GitHub
Copy the commands from GitHub and run them:

```bash
git remote add origin https://github.com/p4r1ch4y/job-portal.git
git branch -M main
git push -u origin main
```

✅ **Checkpoint**: Your code should now be visible on GitHub

---

## 🗄️ **STEP 2: Deploy Backend to Railway (5 minutes)**

### 2.1 Connect GitHub to Railway
1. Go to [Railway.app](https://railway.app)
2. Click "Login" → "Login with GitHub"
3. Click "New Project"
4. Click "Deploy from GitHub repo"
5. Select your `job-portal` repository
6. Click "Deploy Now"

### 2.2 Configure Railway Project
1. After deployment starts, click on your project
2. Click "Settings" → "Environment"
3. Add these environment variables:

```
MONGODB_URI = mongodb+srv://YOUR_USERNAME:<EMAIL>/jobportal?retryWrites=true&w=majority
JWT_SECRET = your-super-secret-jwt-key-at-least-32-characters-long
NODE_ENV = production
PORT = 5000
```

**Optional (for external jobs):**
```
RAPIDAPI_KEY = your-rapidapi-key-if-you-have-one
```

### 2.3 Set Root Directory
1. In Railway project settings
2. Go to "Settings" → "Service"
3. Set "Root Directory" to `backend`
4. Click "Save"

### 2.4 Get Your Backend URL
1. Go to "Settings" → "Networking"
2. Click "Generate Domain"
3. Copy your Railway URL (e.g., `https://job-portal-production-xxxx.up.railway.app`)
4. **SAVE THIS URL** - you'll need it for frontend!

✅ **Checkpoint**: Visit your Railway URL - you should see "Job Portal API is running!"

---

## 🌐 **STEP 3: Deploy Frontend to Vercel (5 minutes)**

### 3.1 Connect GitHub to Vercel
1. Go to [Vercel.com](https://vercel.com)
2. Click "Login" → "Continue with GitHub"
3. Click "New Project"
4. Find your `job-portal` repository
5. Click "Import"

### 3.2 Configure Vercel Project
1. **Project Name**: `job-portal`
2. **Framework Preset**: `Vite`
3. **Root Directory**: `frontend`
4. **Build Command**: `npm run build`
5. **Output Directory**: `dist`
6. Click "Deploy"

### 3.3 Add Environment Variables
1. After deployment, go to your project dashboard
2. Click "Settings" → "Environment Variables"
3. Add this variable:

```
Name: VITE_API_URL
Value: https://your-railway-url.up.railway.app/api
```

**Replace `your-railway-url` with your actual Railway URL from Step 2.4!**

### 3.4 Redeploy
1. Go to "Deployments" tab
2. Click the three dots on the latest deployment
3. Click "Redeploy"

✅ **Checkpoint**: Your frontend should now be live on Vercel!

---

## 🧪 **STEP 4: Test Your Live Application (5 minutes)**

### 4.1 Test Backend
Visit your Railway URL and test these endpoints:
- `https://your-railway-url.up.railway.app/` → Should show "Job Portal API is running!"
- `https://your-railway-url.up.railway.app/api/jobs` → Should return empty jobs array

### 4.2 Test Frontend
Visit your Vercel URL and test:
1. **Homepage loads** ✅
2. **Register as Candidate** ✅
3. **Register as Employer** (use different email) ✅
4. **Login as Employer** ✅
5. **Post a Job** ✅
6. **Login as Candidate** ✅
7. **View and Apply for Job** ✅

---

## 🎉 **CONGRATULATIONS! YOUR JOB PORTAL IS LIVE!**

### 🔗 **Your Live URLs:**
- **Frontend**: `https://your-project.vercel.app`
- **Backend**: `https://your-project.railway.app`

### 📱 **Share Your App:**
Your Job Portal is now live and ready to use! You can share the Vercel URL with anyone.

---

## 🚨 **Troubleshooting**

### Problem: "API calls failing"
**Solution**: Check that `VITE_API_URL` in Vercel matches your Railway URL exactly

### Problem: "Database connection error"
**Solution**: Verify your MongoDB Atlas connection string and IP whitelist

### Problem: "Build failed"
**Solution**: Check the build logs in Vercel/Railway dashboard

### Problem: "CORS errors"
**Solution**: Your Railway backend should automatically allow your Vercel domain

---

## 📞 **Need Help?**

If you encounter any issues:
1. Check the deployment logs in Railway/Vercel dashboards
2. Verify all environment variables are set correctly
3. Make sure your MongoDB Atlas cluster is running
4. Check that your GitHub repository has all the latest code

---

## 🎯 **What's Next?**

Your Job Portal is now production-ready! Consider:
1. Setting up a custom domain
2. Adding email notifications
3. Implementing analytics
4. Adding more external job providers
5. Setting up monitoring and alerts

**Enjoy your live Job Portal! 🚀**
