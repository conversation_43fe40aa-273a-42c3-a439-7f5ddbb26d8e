# ⚡ Quick Deploy Guide

## 🚀 Deploy in 15 Minutes

### 1. Database Setup (5 minutes)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create free account → Create cluster → Create user → Get connection string
3. Save connection string: `mongodb+srv://username:<EMAIL>/jobportal`

### 2. Backend Deploy (5 minutes)
1. Go to [Railway](https://railway.app)
2. Connect GitHub → Select repository → Choose `backend` folder
3. Set environment variables:
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: Any long random string (32+ chars)
   - `NODE_ENV`: `production`
   - `PORT`: `5000`
4. Deploy automatically
5. Save your Railway URL: `https://your-app.railway.app`

### 3. Frontend Deploy (5 minutes)
1. Go to [Vercel](https://vercel.com)
2. Import GitHub repository → Set root directory to `frontend`
3. Set environment variable:
   - `VITE_API_URL`: `https://your-app.railway.app/api`
4. Deploy
5. Your app is live at: `https://your-app.vercel.app`

## 🧪 Test Your Deployment
1. Visit your Vercel URL
2. Register as a candidate
3. Register as an employer (different email)
4. Post a job as employer
5. Apply for job as candidate

## 🎉 Done!
Your Job Portal is now live and ready to use!

## 📞 Need Help?
- Check `DEPLOYMENT_CHECKLIST.md` for detailed steps
- Check `DEPLOYMENT_GUIDE.md` for troubleshooting
- Review logs in Railway and Vercel dashboards
