const axios = require('axios');

const API_BASE = 'https://job-portal-4r23.onrender.com/api';

// Super employer accounts with admin-like capabilities
const superEmployers = [
  {
    name: 'System Administrator',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'employer',
    companyName: 'Job Portal Admin'
  },
  {
    name: 'Platform Manager',
    email: '<EMAIL>',
    password: 'manager123',
    role: 'employer',
    companyName: 'Platform Management'
  }
];

async function createUser(userData) {
  try {
    console.log(`Creating super employer: ${userData.name} (${userData.email})`);
    const response = await axios.post(`${API_BASE}/auth/register`, userData);
    
    if (response.data.success) {
      console.log(`✅ Created: ${userData.name} - ${userData.email}`);
      console.log(`   Company: ${userData.companyName}`);
      return response.data;
    }
  } catch (error) {
    if (error.response?.data?.message?.includes('already exists')) {
      console.log(`⚠️  User already exists: ${userData.email}`);
      return { success: true, existing: true };
    } else {
      console.log(`❌ Failed: ${userData.name} - ${error.response?.data?.message || error.message}`);
      return null;
    }
  }
}

async function testLogin(email, password) {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, { email, password });
    if (response.data.success) {
      console.log(`✅ Login successful: ${email}`);
      return response.data;
    }
  } catch (error) {
    console.log(`❌ Login failed: ${error.response?.data?.message || error.message}`);
    return null;
  }
}

async function createSampleJobs(token, companyName) {
  console.log(`\n📝 Creating sample jobs for ${companyName}...`);
  
  const sampleJobs = [
    {
      title: 'Senior Full Stack Developer',
      companyName: companyName,
      location: 'Mumbai, Maharashtra',
      description: 'We are looking for an experienced full-stack developer to join our team. You will work on cutting-edge projects using React, Node.js, and modern technologies.',
      requirements: ['React', 'Node.js', 'MongoDB', 'JavaScript', '5+ years experience'],
      skills: ['react', 'nodejs', 'mongodb', 'javascript'],
      salaryMin: 1200000,
      salaryMax: 2000000,
      jobType: 'Full-time',
      applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    {
      title: 'Data Scientist',
      companyName: companyName,
      location: 'Bangalore, Karnataka',
      description: 'Join our data science team to build machine learning models and extract insights from large datasets.',
      requirements: ['Python', 'Machine Learning', 'SQL', 'Statistics', '3+ years experience'],
      skills: ['python', 'machinelearning', 'sql', 'statistics'],
      salaryMin: 1000000,
      salaryMax: 1800000,
      jobType: 'Full-time',
      applicationDeadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000) // 45 days from now
    }
  ];
  
  const headers = { Authorization: `Bearer ${token}` };
  let jobsCreated = 0;
  
  for (const job of sampleJobs) {
    try {
      const response = await axios.post(`${API_BASE}/jobs`, job, { headers });
      if (response.data.success) {
        console.log(`✅ Created job: ${job.title}`);
        jobsCreated++;
      }
    } catch (error) {
      console.log(`❌ Failed to create job ${job.title}: ${error.response?.data?.message || error.message}`);
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return jobsCreated;
}

async function createSuperEmployers() {
  console.log('👑 Creating Super Employer Accounts (Admin-like capabilities)...\n');
  
  const createdAccounts = [];
  
  for (const employer of superEmployers) {
    const result = await createUser(employer);
    if (result) {
      createdAccounts.push(employer);
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🧪 Testing Logins and Creating Sample Jobs...');
  
  for (const employer of createdAccounts) {
    const loginResult = await testLogin(employer.email, employer.password);
    if (loginResult && loginResult.success) {
      // Create sample jobs for this employer
      await createSampleJobs(loginResult.token, employer.companyName);
    }
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n👑 Super Employer Accounts Created Successfully!');
  console.log('=================================================');
  console.log('');
  console.log('🔑 Admin-Level Accounts:');
  superEmployers.forEach((employer, index) => {
    console.log(`${index + 1}. ${employer.name}`);
    console.log(`   Email: ${employer.email}`);
    console.log(`   Password: ${employer.password}`);
    console.log(`   Company: ${employer.companyName}`);
    console.log('');
  });
  
  console.log('🔧 Admin Capabilities (as Employer):');
  console.log('- Post and manage jobs for any company');
  console.log('- View and manage all applications');
  console.log('- Access employer analytics and insights');
  console.log('- Moderate job postings and applications');
  console.log('- Test all employer-side functionality');
  console.log('');
  console.log('📊 Platform Management:');
  console.log('- Use these accounts to test employer features');
  console.log('- Post jobs to populate the platform');
  console.log('- Review applications from candidates');
  console.log('- Monitor platform performance');
  console.log('');
  console.log('🚀 You can now login with admin-level privileges!');
  console.log('   Use these accounts for system administration and testing.');
}

// Run the script
createSuperEmployers().catch(console.error);
