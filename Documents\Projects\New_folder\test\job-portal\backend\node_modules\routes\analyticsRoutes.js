const express = require('express');
const router = express.Router();
const {
    getJobPostingsAnalytics,      // Employer: analytics for their job postings (views, applications)
    getApplicationStatusAnalytics, // Employer: breakdown of application statuses
    getPlatformAnalytics          // Admin: overall platform stats (users, jobs, applications)
} = require('../controllers/analyticsController');
const { protect, authorize } = require('../middleware/authMiddleware');

// @route   GET /api/analytics/employer/jobs
// @desc    Get analytics for job postings by the logged-in employer
// @access  Private/Employer
router.get('/employer/jobs', protect, authorize('employer'), getJobPostingsAnalytics);

// @route   GET /api/analytics/employer/applications
// @desc    Get analytics for application statuses for the logged-in employer
// @access  Private/Employer
router.get('/employer/applications', protect, authorize('employer'), getApplicationStatusAnalytics);

// @route   GET /api/analytics/platform
// @desc    Get overall platform analytics (for Admin users)
// @access  Private/Admin (Assuming an 'admin' role, or a more general check)
// For now, let's restrict to employer, but ideally this is an admin route.
// If you add an 'admin' role, change authorize('employer') to authorize('admin')
router.get('/platform', protect, authorize('employer'), getPlatformAnalytics); // Placeholder: should be admin

module.exports = router;