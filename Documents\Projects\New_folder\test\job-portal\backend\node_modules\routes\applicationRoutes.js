const express = require('express');
const router = express.Router();
const {
    applyForJob,
    getApplicationsForJob,       // Employer: sees all applications for their job
    getApplicationsByCandidate,  // Candidate: sees all jobs they applied to
    getApplicationDetails,       // Both: view a specific application
    updateApplicationStatus,     // Employer: updates status (e.g., Shortlisted, Rejected)
    withdrawApplication          // Candidate: withdraws their application
} = require('../controllers/applicationController');
const { protect, authorize } = require('../middleware/authMiddleware');

// @route   POST /api/applications/job/:jobId
// @desc    Candidate applies for a job
// @access  Private/Candidate
router.post('/job/:jobId', protect, authorize('candidate'), applyForJob);

// @route   GET /api/applications/job/:jobId
// @desc    Employer gets all applications for a specific job they posted
// @access  Private/Employer
router.get('/job/:jobId', protect, authorize('employer'), getApplicationsForJob);

// @route   GET /api/applications/candidate/me
// @desc    Candidate gets all their applications
// @access  Private/Candidate
router.get('/candidate/me', protect, authorize('candidate'), getApplicationsByCandidate);

// @route   GET /api/applications/:applicationId
// @desc    Get details of a specific application (accessible by involved candidate or employer)
// @access  Private (Candidate or Employer involved in the application)
router.get('/:applicationId', protect, getApplicationDetails);

// @route   PUT /api/applications/:applicationId/status
// @desc    Employer updates the status of an application
// @access  Private/Employer
router.put('/:applicationId/status', protect, authorize('employer'), updateApplicationStatus);

// @route   DELETE /api/applications/:applicationId/withdraw
// @desc    Candidate withdraws their application
// @access  Private/Candidate
router.delete('/:applicationId/withdraw', protect, authorize('candidate'), withdrawApplication);

module.exports = router;