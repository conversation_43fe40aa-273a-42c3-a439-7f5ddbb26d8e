const express = require('express');
const router = express.Router();
const {
    createJob,
    getJob<PERSON>,
    getJob<PERSON>yId,
    updateJob,
    deleteJob,
    getEmployerJobs,
    getJobsBySkills, // Example of a more specific search route
    incrementJobView // Example for tracking views
} = require('../controllers/jobController');
const { protect, authorize, employer } = require('../middleware/authMiddleware');

// @route   POST /api/jobs
// @desc    Create a new job listing
// @access  Private/Employer
router.post('/', protect, authorize('employer'), createJob); // or protect, employer

// @route   GET /api/jobs
// @desc    Get all active jobs (with search, filter, pagination)
// @access  Public
router.get('/', getJobs);

// @route   GET /api/jobs/employer
// @desc    Get all jobs for the logged-in employer
// @access  Private/Employer
router.get('/employer', protect, authorize('employer'), getEmployerJobs);

// @route   GET /api/jobs/skills
// @desc    Search jobs by skills (example)
// @access  Public
router.get('/skills', getJobsBySkills); // Example, might be part of general getJobs with query params

// @route   GET /api/jobs/:id
// @desc    Get a single job by ID
// @access  Public
router.get('/:id', getJobById);

// @route   PUT /api/jobs/:id/view
// @desc    Increment view count for a job (internal or specific tracking)
// @access  Public (or Private if tracking authenticated views)
router.put('/:id/view', incrementJobView); 

// @route   PUT /api/jobs/:id
// @desc    Update a job listing
// @access  Private/Employer (only the employer who created it)
router.put('/:id', protect, authorize('employer'), updateJob);

// @route   DELETE /api/jobs/:id
// @desc    Delete a job listing (soft or hard delete)
// @access  Private/Employer (only the employer who created it)
router.delete('/:id', protect, authorize('employer'), deleteJob);

module.exports = router;