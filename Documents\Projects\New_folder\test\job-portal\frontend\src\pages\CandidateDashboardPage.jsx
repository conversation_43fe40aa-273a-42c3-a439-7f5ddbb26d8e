import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { applicationService, profileService, jobService } from '../services/api';
import { FaUserEdit, FaFileAlt, FaSearch, FaHeart, FaBriefcase, FaCheckCircle, FaTimesCircle, FaHourglassHalf, FaEye, FaUsers, FaChartLine, FaTrophy } from 'react-icons/fa';
import StatCard from '../components/Dashboard/StatCard';



const ApplicationStatusIcon = ({ status }) => {
    switch (status.toLowerCase()) {
        case 'applied': return <FaHourglassHalf className="text-yellow-500" title="Applied" />;
        case 'viewed': return <FaEye className="text-blue-500" title="Viewed by Employer" />; // Assuming FaEye is imported
        case 'shortlisted': return <FaCheckCircle className="text-teal-500" title="Shortlisted" />;
        case 'interviewing': return <FaUsers className="text-indigo-500" title="Interviewing" />; // Assuming FaUsers is imported
        case 'offered': return <FaCheckCircle className="text-green-500" title="Offered" />;
        case 'rejected': return <FaTimesCircle className="text-red-500" title="Rejected" />;
        case 'withdrawn': return <FaTimesCircle className="text-gray-500" title="Withdrawn" />;
        default: return <FaFileAlt className="text-gray-400" title={status} />;
    }
};

const CandidateDashboardPage = () => {
  const { user } = useAuth();
  const [applications, setApplications] = useState([]);
  const [profile, setProfile] = useState(null);
  const [recentJobs, setRecentJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalApplications: 0,
    activeProcesses: 0,
    profileCompletion: 0,
    responseRate: 0
  });

  useEffect(() => {
    const fetchCandidateData = async () => {
      if (!user || user.role !== 'candidate') return;
      setLoading(true);
      setError(null);
      try {
        // Fetch candidate's applications
        const appsResponse = await applicationService.getMyApplications();
        const applicationsData = appsResponse.data || [];
        setApplications(applicationsData);

        // Fetch candidate's profile
        try {
            const profileResponse = await profileService.getMyProfile();
            setProfile(profileResponse.data);
        } catch (profileError) {
            if (profileError.response && profileError.response.status === 404) {
                setProfile(null); // Profile not yet created
            } else {
                throw profileError; // Re-throw other errors
            }
        }

        // Fetch recent jobs for recommendations
        try {
            const jobsResponse = await jobService.getJobs({ limit: 5, sort: '-postedDate' });
            setRecentJobs(jobsResponse.data.jobs || []);
        } catch (jobError) {
            console.warn('Failed to fetch recent jobs:', jobError);
        }

        // Calculate stats
        const totalApplications = applicationsData.length;
        const activeProcesses = applicationsData.filter(app =>
          ['shortlisted', 'interviewing', 'offered'].includes(app.status?.toLowerCase())
        ).length;
        const responseRate = totalApplications > 0
          ? Math.round((applicationsData.filter(app => app.status !== 'applied').length / totalApplications) * 100)
          : 0;

        setStats({
          totalApplications,
          activeProcesses,
          profileCompletion: calculateProfileCompletion(profile),
          responseRate
        });

      } catch (err) {
        console.error("Error fetching candidate data:", err);
        setError(err.response?.data?.message || 'Failed to load dashboard data.');
        setApplications([]);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCandidateData();
  }, [user]);

  const calculateProfileCompletion = (profile) => {
    if (!profile) return 0;
    const fields = ['headline', 'skills', 'experience', 'education', 'resumeUrl'];
    const completedFields = fields.filter(field => profile[field] && profile[field].length > 0);
    return Math.round((completedFields.length / fields.length) * 100);
  };

  if (loading) {
    return <p className="text-center py-10">Loading your dashboard...</p>;
  }

  if (!user) {
    return <p className="text-center py-10">Please log in to view your dashboard.</p>;
  }

  const applicationsCount = applications.length;
  const shortlistedCount = applications.filter(app => app.status === 'Shortlisted' || app.status === 'Interviewing' || app.status === 'Offered').length;

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800">Candidate Dashboard</h1>
            <p className="text-gray-600 mt-1">Welcome back, {user.name}!</p>
        </div>
        <Link to="/jobs" className="btn btn-primary mt-4 sm:mt-0 flex items-center">
          <FaSearch className="mr-2" /> Find New Jobs
        </Link>
      </div>

      {error && <p className="text-center text-red-500 bg-red-100 p-4 rounded-md mb-6">{error}</p>}

      {/* Profile Completion Prompt */}
      {!profile && !loading && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded-md" role="alert">
          <div className="flex">
            <div className="py-1"><FaUserEdit className="h-6 w-6 text-yellow-500 mr-3" /></div>
            <div>
              <p className="font-bold">Complete Your Profile</p>
              <p className="text-sm">A complete profile increases your chances of getting noticed by employers. </p>
              <Link to="/candidate/profile" className="font-semibold underline hover:text-yellow-800">
                Create or Update Your Profile Now
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Stats Section */}
      <section className="mb-10">
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">Your Activity</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <StatCard title="Applications Submitted" value={applicationsCount} icon={<FaFileAlt size={24} className="text-blue-500" />} color="border-blue-500" linkTo="/candidate/applications" />
          <StatCard title="Active Processes" value={shortlistedCount} icon={<FaHeart size={24} className="text-green-500" />} color="border-green-500" linkTo="/candidate/applications?status=active" />
          <StatCard title="Your Profile" value={profile ? "View/Edit" : "Create Profile"} icon={<FaUserEdit size={24} className="text-purple-500" />} color="border-purple-500" linkTo="/candidate/profile" />
        </div>
      </section>

      {/* Recent Applications Section */}
      <section>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">Recent Applications</h2>
        {applications.length === 0 && !loading ? (
          <div className="text-center py-10 bg-gray-50 rounded-lg">
            <FaBriefcase size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 text-lg mb-2">You haven't applied for any jobs yet.</p>
            <Link to="/jobs" className="btn btn-primary">
              Start Applying Now
            </Link>
          </div>
        ) : (
          <div className="bg-white shadow-lg rounded-lg overflow-x-auto">
            <table className="w-full table-auto">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Title</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied On</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {applications.slice(0, 5).map(app => ( // Show recent 5, link to all applications page
                  <tr key={app._id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link to={`/jobs/${app.jobId?._id || app.jobId}`} className="text-sm font-medium text-primary hover:underline">
                        {app.jobDetails?.title || app.jobSnapshot?.title || 'Job Title N/A'}
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {app.jobDetails?.companyName || app.jobSnapshot?.companyName || 'Company N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(app.applicationDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className="flex items-center">
                        <ApplicationStatusIcon status={app.status} />
                        <span className="ml-2 capitalize">{app.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link to={`/candidate/applications/${app._id}`} className="text-indigo-600 hover:text-indigo-900">
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {applications.length > 5 && (
                <div className="text-center p-4 border-t border-gray-200">
                    <Link to="/candidate/applications" className="text-primary hover:underline font-medium">
                        View All Applications ({applications.length})
                    </Link>
                </div>
            )}
          </div>
        )}
      </section>

      {/* Recommended Jobs Section (Placeholder) */}
      {/* <section className="mt-10">
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">Recommended Jobs for You</h2>
        <p className="text-gray-600 bg-gray-50 p-6 rounded-lg">Job recommendations based on your profile and activity will appear here.</p>
      </section> */}
    </div>
  );
};

export default CandidateDashboardPage;