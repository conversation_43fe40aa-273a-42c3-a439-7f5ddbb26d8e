const axios = require('axios');

async function testAPI() {
  try {
    console.log('Testing jobs API...');
    
    const response = await axios.get('http://localhost:5000/api/jobs?pageSize=6&sortBy=postedDate:desc');
    
    console.log('Response status:', response.status);
    console.log('Response data structure:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.jobs && response.data.jobs.length > 0) {
      console.log('\n✅ Jobs found:', response.data.jobs.length);
      console.log('First job title:', response.data.jobs[0].title);
    } else {
      console.log('\n❌ No jobs found in response');
    }
    
  } catch (error) {
    console.error('API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testAPI();
