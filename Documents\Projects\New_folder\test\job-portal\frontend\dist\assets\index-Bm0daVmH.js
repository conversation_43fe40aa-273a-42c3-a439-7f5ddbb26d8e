(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))u(f);new MutationObserver(f=>{for(const m of f)if(m.type==="childList")for(const h of m.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&u(h)}).observe(document,{childList:!0,subtree:!0});function o(f){const m={};return f.integrity&&(m.integrity=f.integrity),f.referrerPolicy&&(m.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?m.credentials="include":f.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function u(f){if(f.ep)return;f.ep=!0;const m=o(f);fetch(f.href,m)}})();function O0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var xu={exports:{}},ai={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wm;function I2(){if(Wm)return ai;Wm=1;var n=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function o(u,f,m){var h=null;if(m!==void 0&&(h=""+m),f.key!==void 0&&(h=""+f.key),"key"in f){m={};for(var g in f)g!=="key"&&(m[g]=f[g])}else m=f;return f=m.ref,{$$typeof:n,type:u,key:h,ref:f!==void 0?f:null,props:m}}return ai.Fragment=c,ai.jsx=o,ai.jsxs=o,ai}var Im;function ep(){return Im||(Im=1,xu.exports=I2()),xu.exports}var s=ep(),yu={exports:{}},me={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var e0;function tp(){if(e0)return me;e0=1;var n=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),x=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),N=Symbol.iterator;function S(w){return w===null||typeof w!="object"?null:(w=N&&w[N]||w["@@iterator"],typeof w=="function"?w:null)}var H={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b=Object.assign,E={};function T(w,G,F){this.props=w,this.context=G,this.refs=E,this.updater=F||H}T.prototype.isReactComponent={},T.prototype.setState=function(w,G){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,G,"setState")},T.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function z(){}z.prototype=T.prototype;function M(w,G,F){this.props=w,this.context=G,this.refs=E,this.updater=F||H}var J=M.prototype=new z;J.constructor=M,b(J,T.prototype),J.isPureReactComponent=!0;var k=Array.isArray,Q={H:null,A:null,T:null,S:null,V:null},Z=Object.prototype.hasOwnProperty;function le(w,G,F,K,ne,je){return F=je.ref,{$$typeof:n,type:w,key:G,ref:F!==void 0?F:null,props:je}}function xe(w,G){return le(w.type,G,void 0,void 0,void 0,w.props)}function oe(w){return typeof w=="object"&&w!==null&&w.$$typeof===n}function ze(w){var G={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(F){return G[F]})}var ee=/\/+/g;function ae(w,G){return typeof w=="object"&&w!==null&&w.key!=null?ze(""+w.key):G.toString(36)}function re(){}function Me(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(re,re):(w.status="pending",w.then(function(G){w.status==="pending"&&(w.status="fulfilled",w.value=G)},function(G){w.status==="pending"&&(w.status="rejected",w.reason=G)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function W(w,G,F,K,ne){var je=typeof w;(je==="undefined"||je==="boolean")&&(w=null);var fe=!1;if(w===null)fe=!0;else switch(je){case"bigint":case"string":case"number":fe=!0;break;case"object":switch(w.$$typeof){case n:case c:fe=!0;break;case v:return fe=w._init,W(fe(w._payload),G,F,K,ne)}}if(fe)return ne=ne(w),fe=K===""?"."+ae(w,0):K,k(ne)?(F="",fe!=null&&(F=fe.replace(ee,"$&/")+"/"),W(ne,G,F,"",function(pl){return pl})):ne!=null&&(oe(ne)&&(ne=xe(ne,F+(ne.key==null||w&&w.key===ne.key?"":(""+ne.key).replace(ee,"$&/")+"/")+fe)),G.push(ne)),1;fe=0;var mt=K===""?".":K+":";if(k(w))for(var _e=0;_e<w.length;_e++)K=w[_e],je=mt+ae(K,_e),fe+=W(K,G,F,je,ne);else if(_e=S(w),typeof _e=="function")for(w=_e.call(w),_e=0;!(K=w.next()).done;)K=K.value,je=mt+ae(K,_e++),fe+=W(K,G,F,je,ne);else if(je==="object"){if(typeof w.then=="function")return W(Me(w),G,F,K,ne);throw G=String(w),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return fe}function _(w,G,F){if(w==null)return w;var K=[],ne=0;return W(w,K,"","",function(je){return G.call(F,je,ne++)}),K}function X(w){if(w._status===-1){var G=w._result;G=G(),G.then(function(F){(w._status===0||w._status===-1)&&(w._status=1,w._result=F)},function(F){(w._status===0||w._status===-1)&&(w._status=2,w._result=F)}),w._status===-1&&(w._status=0,w._result=G)}if(w._status===1)return w._result.default;throw w._result}var P=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function te(){}return me.Children={map:_,forEach:function(w,G,F){_(w,function(){G.apply(this,arguments)},F)},count:function(w){var G=0;return _(w,function(){G++}),G},toArray:function(w){return _(w,function(G){return G})||[]},only:function(w){if(!oe(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},me.Component=T,me.Fragment=o,me.Profiler=f,me.PureComponent=M,me.StrictMode=u,me.Suspense=y,me.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Q,me.__COMPILER_RUNTIME={__proto__:null,c:function(w){return Q.H.useMemoCache(w)}},me.cache=function(w){return function(){return w.apply(null,arguments)}},me.cloneElement=function(w,G,F){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var K=b({},w.props),ne=w.key,je=void 0;if(G!=null)for(fe in G.ref!==void 0&&(je=void 0),G.key!==void 0&&(ne=""+G.key),G)!Z.call(G,fe)||fe==="key"||fe==="__self"||fe==="__source"||fe==="ref"&&G.ref===void 0||(K[fe]=G[fe]);var fe=arguments.length-2;if(fe===1)K.children=F;else if(1<fe){for(var mt=Array(fe),_e=0;_e<fe;_e++)mt[_e]=arguments[_e+2];K.children=mt}return le(w.type,ne,void 0,void 0,je,K)},me.createContext=function(w){return w={$$typeof:h,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:m,_context:w},w},me.createElement=function(w,G,F){var K,ne={},je=null;if(G!=null)for(K in G.key!==void 0&&(je=""+G.key),G)Z.call(G,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(ne[K]=G[K]);var fe=arguments.length-2;if(fe===1)ne.children=F;else if(1<fe){for(var mt=Array(fe),_e=0;_e<fe;_e++)mt[_e]=arguments[_e+2];ne.children=mt}if(w&&w.defaultProps)for(K in fe=w.defaultProps,fe)ne[K]===void 0&&(ne[K]=fe[K]);return le(w,je,void 0,void 0,null,ne)},me.createRef=function(){return{current:null}},me.forwardRef=function(w){return{$$typeof:g,render:w}},me.isValidElement=oe,me.lazy=function(w){return{$$typeof:v,_payload:{_status:-1,_result:w},_init:X}},me.memo=function(w,G){return{$$typeof:x,type:w,compare:G===void 0?null:G}},me.startTransition=function(w){var G=Q.T,F={};Q.T=F;try{var K=w(),ne=Q.S;ne!==null&&ne(F,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(te,P)}catch(je){P(je)}finally{Q.T=G}},me.unstable_useCacheRefresh=function(){return Q.H.useCacheRefresh()},me.use=function(w){return Q.H.use(w)},me.useActionState=function(w,G,F){return Q.H.useActionState(w,G,F)},me.useCallback=function(w,G){return Q.H.useCallback(w,G)},me.useContext=function(w){return Q.H.useContext(w)},me.useDebugValue=function(){},me.useDeferredValue=function(w,G){return Q.H.useDeferredValue(w,G)},me.useEffect=function(w,G,F){var K=Q.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(w,G)},me.useId=function(){return Q.H.useId()},me.useImperativeHandle=function(w,G,F){return Q.H.useImperativeHandle(w,G,F)},me.useInsertionEffect=function(w,G){return Q.H.useInsertionEffect(w,G)},me.useLayoutEffect=function(w,G){return Q.H.useLayoutEffect(w,G)},me.useMemo=function(w,G){return Q.H.useMemo(w,G)},me.useOptimistic=function(w,G){return Q.H.useOptimistic(w,G)},me.useReducer=function(w,G,F){return Q.H.useReducer(w,G,F)},me.useRef=function(w){return Q.H.useRef(w)},me.useState=function(w){return Q.H.useState(w)},me.useSyncExternalStore=function(w,G,F){return Q.H.useSyncExternalStore(w,G,F)},me.useTransition=function(){return Q.H.useTransition()},me.version="19.1.0",me}var t0;function Ju(){return t0||(t0=1,yu.exports=tp()),yu.exports}var R=Ju();const Yl=O0(R);var gu={exports:{}},ni={},vu={exports:{}},bu={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var l0;function lp(){return l0||(l0=1,function(n){function c(_,X){var P=_.length;_.push(X);e:for(;0<P;){var te=P-1>>>1,w=_[te];if(0<f(w,X))_[te]=X,_[P]=w,P=te;else break e}}function o(_){return _.length===0?null:_[0]}function u(_){if(_.length===0)return null;var X=_[0],P=_.pop();if(P!==X){_[0]=P;e:for(var te=0,w=_.length,G=w>>>1;te<G;){var F=2*(te+1)-1,K=_[F],ne=F+1,je=_[ne];if(0>f(K,P))ne<w&&0>f(je,K)?(_[te]=je,_[ne]=P,te=ne):(_[te]=K,_[F]=P,te=F);else if(ne<w&&0>f(je,P))_[te]=je,_[ne]=P,te=ne;else break e}}return X}function f(_,X){var P=_.sortIndex-X.sortIndex;return P!==0?P:_.id-X.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;n.unstable_now=function(){return m.now()}}else{var h=Date,g=h.now();n.unstable_now=function(){return h.now()-g}}var y=[],x=[],v=1,N=null,S=3,H=!1,b=!1,E=!1,T=!1,z=typeof setTimeout=="function"?setTimeout:null,M=typeof clearTimeout=="function"?clearTimeout:null,J=typeof setImmediate<"u"?setImmediate:null;function k(_){for(var X=o(x);X!==null;){if(X.callback===null)u(x);else if(X.startTime<=_)u(x),X.sortIndex=X.expirationTime,c(y,X);else break;X=o(x)}}function Q(_){if(E=!1,k(_),!b)if(o(y)!==null)b=!0,Z||(Z=!0,ae());else{var X=o(x);X!==null&&W(Q,X.startTime-_)}}var Z=!1,le=-1,xe=5,oe=-1;function ze(){return T?!0:!(n.unstable_now()-oe<xe)}function ee(){if(T=!1,Z){var _=n.unstable_now();oe=_;var X=!0;try{e:{b=!1,E&&(E=!1,M(le),le=-1),H=!0;var P=S;try{t:{for(k(_),N=o(y);N!==null&&!(N.expirationTime>_&&ze());){var te=N.callback;if(typeof te=="function"){N.callback=null,S=N.priorityLevel;var w=te(N.expirationTime<=_);if(_=n.unstable_now(),typeof w=="function"){N.callback=w,k(_),X=!0;break t}N===o(y)&&u(y),k(_)}else u(y);N=o(y)}if(N!==null)X=!0;else{var G=o(x);G!==null&&W(Q,G.startTime-_),X=!1}}break e}finally{N=null,S=P,H=!1}X=void 0}}finally{X?ae():Z=!1}}}var ae;if(typeof J=="function")ae=function(){J(ee)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,Me=re.port2;re.port1.onmessage=ee,ae=function(){Me.postMessage(null)}}else ae=function(){z(ee,0)};function W(_,X){le=z(function(){_(n.unstable_now())},X)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(_){_.callback=null},n.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):xe=0<_?Math.floor(1e3/_):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_next=function(_){switch(S){case 1:case 2:case 3:var X=3;break;default:X=S}var P=S;S=X;try{return _()}finally{S=P}},n.unstable_requestPaint=function(){T=!0},n.unstable_runWithPriority=function(_,X){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var P=S;S=_;try{return X()}finally{S=P}},n.unstable_scheduleCallback=function(_,X,P){var te=n.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?te+P:te):P=te,_){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=P+w,_={id:v++,callback:X,priorityLevel:_,startTime:P,expirationTime:w,sortIndex:-1},P>te?(_.sortIndex=P,c(x,_),o(y)===null&&_===o(x)&&(E?(M(le),le=-1):E=!0,W(Q,P-te))):(_.sortIndex=w,c(y,_),b||H||(b=!0,Z||(Z=!0,ae()))),_},n.unstable_shouldYield=ze,n.unstable_wrapCallback=function(_){var X=S;return function(){var P=S;S=X;try{return _.apply(this,arguments)}finally{S=P}}}}(bu)),bu}var a0;function ap(){return a0||(a0=1,vu.exports=lp()),vu.exports}var ju={exports:{}},lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n0;function np(){if(n0)return lt;n0=1;var n=Ju();function c(y){var x="https://react.dev/errors/"+y;if(1<arguments.length){x+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)x+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+x+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var u={d:{f:o,r:function(){throw Error(c(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},f=Symbol.for("react.portal");function m(y,x,v){var N=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:N==null?null:""+N,children:y,containerInfo:x,implementation:v}}var h=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,x){if(y==="font")return"";if(typeof x=="string")return x==="use-credentials"?x:""}return lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,lt.createPortal=function(y,x){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!x||x.nodeType!==1&&x.nodeType!==9&&x.nodeType!==11)throw Error(c(299));return m(y,x,null,v)},lt.flushSync=function(y){var x=h.T,v=u.p;try{if(h.T=null,u.p=2,y)return y()}finally{h.T=x,u.p=v,u.d.f()}},lt.preconnect=function(y,x){typeof y=="string"&&(x?(x=x.crossOrigin,x=typeof x=="string"?x==="use-credentials"?x:"":void 0):x=null,u.d.C(y,x))},lt.prefetchDNS=function(y){typeof y=="string"&&u.d.D(y)},lt.preinit=function(y,x){if(typeof y=="string"&&x&&typeof x.as=="string"){var v=x.as,N=g(v,x.crossOrigin),S=typeof x.integrity=="string"?x.integrity:void 0,H=typeof x.fetchPriority=="string"?x.fetchPriority:void 0;v==="style"?u.d.S(y,typeof x.precedence=="string"?x.precedence:void 0,{crossOrigin:N,integrity:S,fetchPriority:H}):v==="script"&&u.d.X(y,{crossOrigin:N,integrity:S,fetchPriority:H,nonce:typeof x.nonce=="string"?x.nonce:void 0})}},lt.preinitModule=function(y,x){if(typeof y=="string")if(typeof x=="object"&&x!==null){if(x.as==null||x.as==="script"){var v=g(x.as,x.crossOrigin);u.d.M(y,{crossOrigin:v,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0})}}else x==null&&u.d.M(y)},lt.preload=function(y,x){if(typeof y=="string"&&typeof x=="object"&&x!==null&&typeof x.as=="string"){var v=x.as,N=g(v,x.crossOrigin);u.d.L(y,v,{crossOrigin:N,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0,type:typeof x.type=="string"?x.type:void 0,fetchPriority:typeof x.fetchPriority=="string"?x.fetchPriority:void 0,referrerPolicy:typeof x.referrerPolicy=="string"?x.referrerPolicy:void 0,imageSrcSet:typeof x.imageSrcSet=="string"?x.imageSrcSet:void 0,imageSizes:typeof x.imageSizes=="string"?x.imageSizes:void 0,media:typeof x.media=="string"?x.media:void 0})}},lt.preloadModule=function(y,x){if(typeof y=="string")if(x){var v=g(x.as,x.crossOrigin);u.d.m(y,{as:typeof x.as=="string"&&x.as!=="script"?x.as:void 0,crossOrigin:v,integrity:typeof x.integrity=="string"?x.integrity:void 0})}else u.d.m(y)},lt.requestFormReset=function(y){u.d.r(y)},lt.unstable_batchedUpdates=function(y,x){return y(x)},lt.useFormState=function(y,x,v){return h.H.useFormState(y,x,v)},lt.useFormStatus=function(){return h.H.useHostTransitionStatus()},lt.version="19.1.0",lt}var i0;function ip(){if(i0)return ju.exports;i0=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(c){console.error(c)}}return n(),ju.exports=np(),ju.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s0;function sp(){if(s0)return ni;s0=1;var n=ap(),c=Ju(),o=ip();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(m(e)!==e)throw Error(u(188))}function y(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(u(188));return t!==e?null:e}for(var l=e,a=t;;){var i=l.return;if(i===null)break;var r=i.alternate;if(r===null){if(a=i.return,a!==null){l=a;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===l)return g(i),e;if(r===a)return g(i),t;r=r.sibling}throw Error(u(188))}if(l.return!==a.return)l=i,a=r;else{for(var d=!1,p=i.child;p;){if(p===l){d=!0,l=i,a=r;break}if(p===a){d=!0,a=i,l=r;break}p=p.sibling}if(!d){for(p=r.child;p;){if(p===l){d=!0,l=r,a=i;break}if(p===a){d=!0,a=r,l=i;break}p=p.sibling}if(!d)throw Error(u(189))}}if(l.alternate!==a)throw Error(u(190))}if(l.tag!==3)throw Error(u(188));return l.stateNode.current===l?e:t}function x(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=x(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,N=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),b=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),z=Symbol.for("react.provider"),M=Symbol.for("react.consumer"),J=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),Q=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),le=Symbol.for("react.memo"),xe=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),ze=Symbol.for("react.memo_cache_sentinel"),ee=Symbol.iterator;function ae(e){return e===null||typeof e!="object"?null:(e=ee&&e[ee]||e["@@iterator"],typeof e=="function"?e:null)}var re=Symbol.for("react.client.reference");function Me(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===re?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case b:return"Fragment";case T:return"Profiler";case E:return"StrictMode";case Q:return"Suspense";case Z:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case H:return"Portal";case J:return(e.displayName||"Context")+".Provider";case M:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case le:return t=e.displayName||null,t!==null?t:Me(e.type)||"Memo";case xe:t=e._payload,e=e._init;try{return Me(e(t))}catch{}}return null}var W=Array.isArray,_=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},te=[],w=-1;function G(e){return{current:e}}function F(e){0>w||(e.current=te[w],te[w]=null,w--)}function K(e,t){w++,te[w]=e.current,e.current=t}var ne=G(null),je=G(null),fe=G(null),mt=G(null);function _e(e,t){switch(K(fe,t),K(je,e),K(ne,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Am(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Am(t),e=Tm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(ne),K(ne,e)}function pl(){F(ne),F(je),F(fe)}function tr(e){e.memoizedState!==null&&K(mt,e);var t=ne.current,l=Tm(t,e.type);t!==l&&(K(je,e),K(ne,l))}function yi(e){je.current===e&&(F(ne),F(je)),mt.current===e&&(F(mt),Wn._currentValue=P)}var lr=Object.prototype.hasOwnProperty,ar=n.unstable_scheduleCallback,nr=n.unstable_cancelCallback,Oh=n.unstable_shouldYield,Dh=n.unstable_requestPaint,Xt=n.unstable_now,Mh=n.unstable_getCurrentPriorityLevel,no=n.unstable_ImmediatePriority,io=n.unstable_UserBlockingPriority,gi=n.unstable_NormalPriority,_h=n.unstable_LowPriority,so=n.unstable_IdlePriority,Uh=n.log,Lh=n.unstable_setDisableYieldValue,sn=null,ht=null;function xl(e){if(typeof Uh=="function"&&Lh(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(sn,e)}catch{}}var pt=Math.clz32?Math.clz32:qh,Hh=Math.log,Bh=Math.LN2;function qh(e){return e>>>=0,e===0?32:31-(Hh(e)/Bh|0)|0}var vi=256,bi=4194304;function Jl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ji(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var i=0,r=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var p=a&134217727;return p!==0?(a=p&~r,a!==0?i=Jl(a):(d&=p,d!==0?i=Jl(d):l||(l=p&~e,l!==0&&(i=Jl(l))))):(p=a&~r,p!==0?i=Jl(p):d!==0?i=Jl(d):l||(l=a&~e,l!==0&&(i=Jl(l)))),i===0?0:t!==0&&t!==i&&(t&r)===0&&(r=i&-i,l=t&-t,r>=l||r===32&&(l&4194048)!==0)?t:i}function rn(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Yh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ro(){var e=vi;return vi<<=1,(vi&4194048)===0&&(vi=256),e}function co(){var e=bi;return bi<<=1,(bi&62914560)===0&&(bi=4194304),e}function ir(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function cn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Vh(e,t,l,a,i,r){var d=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var p=e.entanglements,j=e.expirationTimes,D=e.hiddenUpdates;for(l=d&~l;0<l;){var q=31-pt(l),V=1<<q;p[q]=0,j[q]=-1;var U=D[q];if(U!==null)for(D[q]=null,q=0;q<U.length;q++){var L=U[q];L!==null&&(L.lane&=-536870913)}l&=~V}a!==0&&uo(e,a,0),r!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=r&~(d&~t))}function uo(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-pt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function oo(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-pt(l),i=1<<a;i&t|e[a]&t&&(e[a]|=t),l&=~i}}function sr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function rr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function fo(){var e=X.p;return e!==0?e:(e=window.event,e===void 0?32:km(e.type))}function Gh(e,t){var l=X.p;try{return X.p=e,t()}finally{X.p=l}}var yl=Math.random().toString(36).slice(2),et="__reactFiber$"+yl,st="__reactProps$"+yl,pa="__reactContainer$"+yl,cr="__reactEvents$"+yl,Xh="__reactListeners$"+yl,Jh="__reactHandles$"+yl,mo="__reactResources$"+yl,un="__reactMarker$"+yl;function ur(e){delete e[et],delete e[st],delete e[cr],delete e[Xh],delete e[Jh]}function xa(e){var t=e[et];if(t)return t;for(var l=e.parentNode;l;){if(t=l[pa]||l[et]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Om(e);e!==null;){if(l=e[et])return l;e=Om(e)}return t}e=l,l=e.parentNode}return null}function ya(e){if(e=e[et]||e[pa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function on(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function ga(e){var t=e[mo];return t||(t=e[mo]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[un]=!0}var ho=new Set,po={};function Ql(e,t){va(e,t),va(e+"Capture",t)}function va(e,t){for(po[e]=t,e=0;e<t.length;e++)ho.add(t[e])}var Qh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),xo={},yo={};function kh(e){return lr.call(yo,e)?!0:lr.call(xo,e)?!1:Qh.test(e)?yo[e]=!0:(xo[e]=!0,!1)}function Ni(e,t,l){if(kh(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Si(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function $t(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var or,go;function ba(e){if(or===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);or=t&&t[1]||"",go=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+or+e+go}var fr=!1;function dr(e,t){if(!e||fr)return"";fr=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(L){var U=L}Reflect.construct(e,[],V)}else{try{V.call()}catch(L){U=L}e.call(V.prototype)}}else{try{throw Error()}catch(L){U=L}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(L){if(L&&U&&typeof L.stack=="string")return[L.stack,U.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=a.DetermineComponentFrameRoot(),d=r[0],p=r[1];if(d&&p){var j=d.split(`
`),D=p.split(`
`);for(i=a=0;a<j.length&&!j[a].includes("DetermineComponentFrameRoot");)a++;for(;i<D.length&&!D[i].includes("DetermineComponentFrameRoot");)i++;if(a===j.length||i===D.length)for(a=j.length-1,i=D.length-1;1<=a&&0<=i&&j[a]!==D[i];)i--;for(;1<=a&&0<=i;a--,i--)if(j[a]!==D[i]){if(a!==1||i!==1)do if(a--,i--,0>i||j[a]!==D[i]){var q=`
`+j[a].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=a&&0<=i);break}}}finally{fr=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?ba(l):""}function Zh(e){switch(e.tag){case 26:case 27:case 5:return ba(e.type);case 16:return ba("Lazy");case 13:return ba("Suspense");case 19:return ba("SuspenseList");case 0:case 15:return dr(e.type,!1);case 11:return dr(e.type.render,!1);case 1:return dr(e.type,!0);case 31:return ba("Activity");default:return""}}function vo(e){try{var t="";do t+=Zh(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function At(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function bo(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Kh(e){var t=bo(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var i=l.get,r=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(d){a=""+d,r.call(this,d)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(d){a=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ei(e){e._valueTracker||(e._valueTracker=Kh(e))}function jo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=bo(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function wi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Fh=/[\n"\\]/g;function Tt(e){return e.replace(Fh,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function mr(e,t,l,a,i,r,d,p){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+At(t)):e.value!==""+At(t)&&(e.value=""+At(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?hr(e,d,At(t)):l!=null?hr(e,d,At(l)):a!=null&&e.removeAttribute("value"),i==null&&r!=null&&(e.defaultChecked=!!r),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+At(p):e.removeAttribute("name")}function No(e,t,l,a,i,r,d,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||l!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;l=l!=null?""+At(l):"",t=t!=null?""+At(t):l,p||t===e.value||(e.value=t),e.defaultValue=t}a=a??i,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=p?e.checked:!!a,e.defaultChecked=!!a,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function hr(e,t,l){t==="number"&&wi(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function ja(e,t,l,a){if(e=e.options,t){t={};for(var i=0;i<l.length;i++)t["$"+l[i]]=!0;for(l=0;l<e.length;l++)i=t.hasOwnProperty("$"+e[l].value),e[l].selected!==i&&(e[l].selected=i),i&&a&&(e[l].defaultSelected=!0)}else{for(l=""+At(l),t=null,i=0;i<e.length;i++){if(e[i].value===l){e[i].selected=!0,a&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function So(e,t,l){if(t!=null&&(t=""+At(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+At(l):""}function Eo(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(u(92));if(W(a)){if(1<a.length)throw Error(u(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=At(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Na(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var $h=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function wo(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||$h.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Ao(e,t,l){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var i in t)a=t[i],t.hasOwnProperty(i)&&l[i]!==a&&wo(e,i,a)}else for(var r in t)t.hasOwnProperty(r)&&wo(e,r,t[r])}function pr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ph=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Wh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ai(e){return Wh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var xr=null;function yr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Sa=null,Ea=null;function To(e){var t=ya(e);if(t&&(e=t.stateNode)){var l=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(mr(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Tt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var i=a[st]||null;if(!i)throw Error(u(90));mr(a,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&jo(a)}break e;case"textarea":So(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&ja(e,!!l.multiple,t,!1)}}}var gr=!1;function Ro(e,t,l){if(gr)return e(t,l);gr=!0;try{var a=e(t);return a}finally{if(gr=!1,(Sa!==null||Ea!==null)&&(fs(),Sa&&(t=Sa,e=Ea,Ea=Sa=null,To(t),e)))for(t=0;t<e.length;t++)To(e[t])}}function fn(e,t){var l=e.stateNode;if(l===null)return null;var a=l[st]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(u(231,t,typeof l));return l}var Pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vr=!1;if(Pt)try{var dn={};Object.defineProperty(dn,"passive",{get:function(){vr=!0}}),window.addEventListener("test",dn,dn),window.removeEventListener("test",dn,dn)}catch{vr=!1}var gl=null,br=null,Ti=null;function Co(){if(Ti)return Ti;var e,t=br,l=t.length,a,i="value"in gl?gl.value:gl.textContent,r=i.length;for(e=0;e<l&&t[e]===i[e];e++);var d=l-e;for(a=1;a<=d&&t[l-a]===i[r-a];a++);return Ti=i.slice(e,1<a?1-a:void 0)}function Ri(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ci(){return!0}function zo(){return!1}function rt(e){function t(l,a,i,r,d){this._reactName=l,this._targetInst=i,this.type=a,this.nativeEvent=r,this.target=d,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(l=e[p],this[p]=l?l(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ci:zo,this.isPropagationStopped=zo,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Ci)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Ci)},persist:function(){},isPersistent:Ci}),t}var kl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},zi=rt(kl),mn=v({},kl,{view:0,detail:0}),Ih=rt(mn),jr,Nr,hn,Oi=v({},mn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Er,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==hn&&(hn&&e.type==="mousemove"?(jr=e.screenX-hn.screenX,Nr=e.screenY-hn.screenY):Nr=jr=0,hn=e),jr)},movementY:function(e){return"movementY"in e?e.movementY:Nr}}),Oo=rt(Oi),e1=v({},Oi,{dataTransfer:0}),t1=rt(e1),l1=v({},mn,{relatedTarget:0}),Sr=rt(l1),a1=v({},kl,{animationName:0,elapsedTime:0,pseudoElement:0}),n1=rt(a1),i1=v({},kl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),s1=rt(i1),r1=v({},kl,{data:0}),Do=rt(r1),c1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},u1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},o1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function f1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=o1[e])?!!t[e]:!1}function Er(){return f1}var d1=v({},mn,{key:function(e){if(e.key){var t=c1[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ri(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?u1[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Er,charCode:function(e){return e.type==="keypress"?Ri(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ri(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),m1=rt(d1),h1=v({},Oi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Mo=rt(h1),p1=v({},mn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Er}),x1=rt(p1),y1=v({},kl,{propertyName:0,elapsedTime:0,pseudoElement:0}),g1=rt(y1),v1=v({},Oi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),b1=rt(v1),j1=v({},kl,{newState:0,oldState:0}),N1=rt(j1),S1=[9,13,27,32],wr=Pt&&"CompositionEvent"in window,pn=null;Pt&&"documentMode"in document&&(pn=document.documentMode);var E1=Pt&&"TextEvent"in window&&!pn,_o=Pt&&(!wr||pn&&8<pn&&11>=pn),Uo=" ",Lo=!1;function Ho(e,t){switch(e){case"keyup":return S1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wa=!1;function w1(e,t){switch(e){case"compositionend":return Bo(t);case"keypress":return t.which!==32?null:(Lo=!0,Uo);case"textInput":return e=t.data,e===Uo&&Lo?null:e;default:return null}}function A1(e,t){if(wa)return e==="compositionend"||!wr&&Ho(e,t)?(e=Co(),Ti=br=gl=null,wa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _o&&t.locale!=="ko"?null:t.data;default:return null}}var T1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!T1[e.type]:t==="textarea"}function Yo(e,t,l,a){Sa?Ea?Ea.push(a):Ea=[a]:Sa=a,t=ys(t,"onChange"),0<t.length&&(l=new zi("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var xn=null,yn=null;function R1(e){jm(e,0)}function Di(e){var t=on(e);if(jo(t))return e}function Vo(e,t){if(e==="change")return t}var Go=!1;if(Pt){var Ar;if(Pt){var Tr="oninput"in document;if(!Tr){var Xo=document.createElement("div");Xo.setAttribute("oninput","return;"),Tr=typeof Xo.oninput=="function"}Ar=Tr}else Ar=!1;Go=Ar&&(!document.documentMode||9<document.documentMode)}function Jo(){xn&&(xn.detachEvent("onpropertychange",Qo),yn=xn=null)}function Qo(e){if(e.propertyName==="value"&&Di(yn)){var t=[];Yo(t,yn,e,yr(e)),Ro(R1,t)}}function C1(e,t,l){e==="focusin"?(Jo(),xn=t,yn=l,xn.attachEvent("onpropertychange",Qo)):e==="focusout"&&Jo()}function z1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Di(yn)}function O1(e,t){if(e==="click")return Di(t)}function D1(e,t){if(e==="input"||e==="change")return Di(t)}function M1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:M1;function gn(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var i=l[a];if(!lr.call(t,i)||!xt(e[i],t[i]))return!1}return!0}function ko(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zo(e,t){var l=ko(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=ko(l)}}function Ko(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ko(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Fo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=wi(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=wi(e.document)}return t}function Rr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var _1=Pt&&"documentMode"in document&&11>=document.documentMode,Aa=null,Cr=null,vn=null,zr=!1;function $o(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;zr||Aa==null||Aa!==wi(a)||(a=Aa,"selectionStart"in a&&Rr(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),vn&&gn(vn,a)||(vn=a,a=ys(Cr,"onSelect"),0<a.length&&(t=new zi("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Aa)))}function Zl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var Ta={animationend:Zl("Animation","AnimationEnd"),animationiteration:Zl("Animation","AnimationIteration"),animationstart:Zl("Animation","AnimationStart"),transitionrun:Zl("Transition","TransitionRun"),transitionstart:Zl("Transition","TransitionStart"),transitioncancel:Zl("Transition","TransitionCancel"),transitionend:Zl("Transition","TransitionEnd")},Or={},Po={};Pt&&(Po=document.createElement("div").style,"AnimationEvent"in window||(delete Ta.animationend.animation,delete Ta.animationiteration.animation,delete Ta.animationstart.animation),"TransitionEvent"in window||delete Ta.transitionend.transition);function Kl(e){if(Or[e])return Or[e];if(!Ta[e])return e;var t=Ta[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Po)return Or[e]=t[l];return e}var Wo=Kl("animationend"),Io=Kl("animationiteration"),ef=Kl("animationstart"),U1=Kl("transitionrun"),L1=Kl("transitionstart"),H1=Kl("transitioncancel"),tf=Kl("transitionend"),lf=new Map,Dr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Dr.push("scrollEnd");function Lt(e,t){lf.set(e,t),Ql(t,[e])}var af=new WeakMap;function Rt(e,t){if(typeof e=="object"&&e!==null){var l=af.get(e);return l!==void 0?l:(t={value:e,source:t,stack:vo(t)},af.set(e,t),t)}return{value:e,source:t,stack:vo(t)}}var Ct=[],Ra=0,Mr=0;function Mi(){for(var e=Ra,t=Mr=Ra=0;t<e;){var l=Ct[t];Ct[t++]=null;var a=Ct[t];Ct[t++]=null;var i=Ct[t];Ct[t++]=null;var r=Ct[t];if(Ct[t++]=null,a!==null&&i!==null){var d=a.pending;d===null?i.next=i:(i.next=d.next,d.next=i),a.pending=i}r!==0&&nf(l,i,r)}}function _i(e,t,l,a){Ct[Ra++]=e,Ct[Ra++]=t,Ct[Ra++]=l,Ct[Ra++]=a,Mr|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function _r(e,t,l,a){return _i(e,t,l,a),Ui(e)}function Ca(e,t){return _i(e,null,null,t),Ui(e)}function nf(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var i=!1,r=e.return;r!==null;)r.childLanes|=l,a=r.alternate,a!==null&&(a.childLanes|=l),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(i=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,i&&t!==null&&(i=31-pt(l),e=r.hiddenUpdates,a=e[i],a===null?e[i]=[t]:a.push(t),t.lane=l|536870912),r):null}function Ui(e){if(50<Jn)throw Jn=0,Yc=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var za={};function B1(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,l,a){return new B1(e,t,l,a)}function Ur(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wt(e,t){var l=e.alternate;return l===null?(l=yt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function sf(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Li(e,t,l,a,i,r){var d=0;if(a=e,typeof e=="function")Ur(e)&&(d=1);else if(typeof e=="string")d=Y2(e,l,ne.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=yt(31,l,t,i),e.elementType=oe,e.lanes=r,e;case b:return Fl(l.children,i,r,t);case E:d=8,i|=24;break;case T:return e=yt(12,l,t,i|2),e.elementType=T,e.lanes=r,e;case Q:return e=yt(13,l,t,i),e.elementType=Q,e.lanes=r,e;case Z:return e=yt(19,l,t,i),e.elementType=Z,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case z:case J:d=10;break e;case M:d=9;break e;case k:d=11;break e;case le:d=14;break e;case xe:d=16,a=null;break e}d=29,l=Error(u(130,e===null?"null":typeof e,"")),a=null}return t=yt(d,l,t,i),t.elementType=e,t.type=a,t.lanes=r,t}function Fl(e,t,l,a){return e=yt(7,e,a,t),e.lanes=l,e}function Lr(e,t,l){return e=yt(6,e,null,t),e.lanes=l,e}function Hr(e,t,l){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Oa=[],Da=0,Hi=null,Bi=0,zt=[],Ot=0,$l=null,It=1,el="";function Pl(e,t){Oa[Da++]=Bi,Oa[Da++]=Hi,Hi=e,Bi=t}function rf(e,t,l){zt[Ot++]=It,zt[Ot++]=el,zt[Ot++]=$l,$l=e;var a=It;e=el;var i=32-pt(a)-1;a&=~(1<<i),l+=1;var r=32-pt(t)+i;if(30<r){var d=i-i%5;r=(a&(1<<d)-1).toString(32),a>>=d,i-=d,It=1<<32-pt(t)+i|l<<i|a,el=r+e}else It=1<<r|l<<i|a,el=e}function Br(e){e.return!==null&&(Pl(e,1),rf(e,1,0))}function qr(e){for(;e===Hi;)Hi=Oa[--Da],Oa[Da]=null,Bi=Oa[--Da],Oa[Da]=null;for(;e===$l;)$l=zt[--Ot],zt[Ot]=null,el=zt[--Ot],zt[Ot]=null,It=zt[--Ot],zt[Ot]=null}var nt=null,Be=null,Se=!1,Wl=null,Jt=!1,Yr=Error(u(519));function Il(e){var t=Error(u(418,""));throw Nn(Rt(t,e)),Yr}function cf(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[et]=e,t[st]=a,l){case"dialog":ve("cancel",t),ve("close",t);break;case"iframe":case"object":case"embed":ve("load",t);break;case"video":case"audio":for(l=0;l<kn.length;l++)ve(kn[l],t);break;case"source":ve("error",t);break;case"img":case"image":case"link":ve("error",t),ve("load",t);break;case"details":ve("toggle",t);break;case"input":ve("invalid",t),No(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Ei(t);break;case"select":ve("invalid",t);break;case"textarea":ve("invalid",t),Eo(t,a.value,a.defaultValue,a.children),Ei(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||wm(t.textContent,l)?(a.popover!=null&&(ve("beforetoggle",t),ve("toggle",t)),a.onScroll!=null&&ve("scroll",t),a.onScrollEnd!=null&&ve("scrollend",t),a.onClick!=null&&(t.onclick=gs),t=!0):t=!1,t||Il(e)}function uf(e){for(nt=e.return;nt;)switch(nt.tag){case 5:case 13:Jt=!1;return;case 27:case 3:Jt=!0;return;default:nt=nt.return}}function bn(e){if(e!==nt)return!1;if(!Se)return uf(e),Se=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||lu(e.type,e.memoizedProps)),l=!l),l&&Be&&Il(e),uf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){Be=Bt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}Be=null}}else t===27?(t=Be,_l(e.type)?(e=su,su=null,Be=e):Be=t):Be=nt?Bt(e.stateNode.nextSibling):null;return!0}function jn(){Be=nt=null,Se=!1}function of(){var e=Wl;return e!==null&&(ot===null?ot=e:ot.push.apply(ot,e),Wl=null),e}function Nn(e){Wl===null?Wl=[e]:Wl.push(e)}var Vr=G(null),ea=null,tl=null;function vl(e,t,l){K(Vr,t._currentValue),t._currentValue=l}function ll(e){e._currentValue=Vr.current,F(Vr)}function Gr(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Xr(e,t,l,a){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var r=i.dependencies;if(r!==null){var d=i.child;r=r.firstContext;e:for(;r!==null;){var p=r;r=i;for(var j=0;j<t.length;j++)if(p.context===t[j]){r.lanes|=l,p=r.alternate,p!==null&&(p.lanes|=l),Gr(r.return,l,e),a||(d=null);break e}r=p.next}}else if(i.tag===18){if(d=i.return,d===null)throw Error(u(341));d.lanes|=l,r=d.alternate,r!==null&&(r.lanes|=l),Gr(d,l,e),d=null}else d=i.child;if(d!==null)d.return=i;else for(d=i;d!==null;){if(d===e){d=null;break}if(i=d.sibling,i!==null){i.return=d.return,d=i;break}d=d.return}i=d}}function Sn(e,t,l,a){e=null;for(var i=t,r=!1;i!==null;){if(!r){if((i.flags&524288)!==0)r=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var d=i.alternate;if(d===null)throw Error(u(387));if(d=d.memoizedProps,d!==null){var p=i.type;xt(i.pendingProps.value,d.value)||(e!==null?e.push(p):e=[p])}}else if(i===mt.current){if(d=i.alternate,d===null)throw Error(u(387));d.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Wn):e=[Wn])}i=i.return}e!==null&&Xr(t,e,l,a),t.flags|=262144}function qi(e){for(e=e.firstContext;e!==null;){if(!xt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ta(e){ea=e,tl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function tt(e){return ff(ea,e)}function Yi(e,t){return ea===null&&ta(e),ff(e,t)}function ff(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},tl===null){if(e===null)throw Error(u(308));tl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else tl=tl.next=t;return l}var q1=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},Y1=n.unstable_scheduleCallback,V1=n.unstable_NormalPriority,Qe={$$typeof:J,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Jr(){return{controller:new q1,data:new Map,refCount:0}}function En(e){e.refCount--,e.refCount===0&&Y1(V1,function(){e.controller.abort()})}var wn=null,Qr=0,Ma=0,_a=null;function G1(e,t){if(wn===null){var l=wn=[];Qr=0,Ma=Zc(),_a={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Qr++,t.then(df,df),t}function df(){if(--Qr===0&&wn!==null){_a!==null&&(_a.status="fulfilled");var e=wn;wn=null,Ma=0,_a=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function X1(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(i){l.push(i)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var i=0;i<l.length;i++)(0,l[i])(t)},function(i){for(a.status="rejected",a.reason=i,i=0;i<l.length;i++)(0,l[i])(void 0)}),a}var mf=_.S;_.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&G1(e,t),mf!==null&&mf(e,t)};var la=G(null);function kr(){var e=la.current;return e!==null?e:Oe.pooledCache}function Vi(e,t){t===null?K(la,la.current):K(la,t.pool)}function hf(){var e=kr();return e===null?null:{parent:Qe._currentValue,pool:e}}var An=Error(u(460)),pf=Error(u(474)),Gi=Error(u(542)),Zr={then:function(){}};function xf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Xi(){}function yf(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Xi,Xi),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,vf(e),e;default:if(typeof t.status=="string")t.then(Xi,Xi);else{if(e=Oe,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=a}},function(a){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,vf(e),e}throw Tn=t,An}}var Tn=null;function gf(){if(Tn===null)throw Error(u(459));var e=Tn;return Tn=null,e}function vf(e){if(e===An||e===Gi)throw Error(u(483))}var bl=!1;function Kr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Fr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function jl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Nl(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Ee&2)!==0){var i=a.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),a.pending=t,t=Ui(e),nf(e,null,l),t}return _i(e,a,t,l),Ui(e)}function Rn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,oo(e,l)}}function $r(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var i=null,r=null;if(l=l.firstBaseUpdate,l!==null){do{var d={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};r===null?i=r=d:r=r.next=d,l=l.next}while(l!==null);r===null?i=r=t:r=r.next=t}else i=r=t;l={baseState:a.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Pr=!1;function Cn(){if(Pr){var e=_a;if(e!==null)throw e}}function zn(e,t,l,a){Pr=!1;var i=e.updateQueue;bl=!1;var r=i.firstBaseUpdate,d=i.lastBaseUpdate,p=i.shared.pending;if(p!==null){i.shared.pending=null;var j=p,D=j.next;j.next=null,d===null?r=D:d.next=D,d=j;var q=e.alternate;q!==null&&(q=q.updateQueue,p=q.lastBaseUpdate,p!==d&&(p===null?q.firstBaseUpdate=D:p.next=D,q.lastBaseUpdate=j))}if(r!==null){var V=i.baseState;d=0,q=D=j=null,p=r;do{var U=p.lane&-536870913,L=U!==p.lane;if(L?(be&U)===U:(a&U)===U){U!==0&&U===Ma&&(Pr=!0),q!==null&&(q=q.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var ce=e,ie=p;U=t;var Re=l;switch(ie.tag){case 1:if(ce=ie.payload,typeof ce=="function"){V=ce.call(Re,V,U);break e}V=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=ie.payload,U=typeof ce=="function"?ce.call(Re,V,U):ce,U==null)break e;V=v({},V,U);break e;case 2:bl=!0}}U=p.callback,U!==null&&(e.flags|=64,L&&(e.flags|=8192),L=i.callbacks,L===null?i.callbacks=[U]:L.push(U))}else L={lane:U,tag:p.tag,payload:p.payload,callback:p.callback,next:null},q===null?(D=q=L,j=V):q=q.next=L,d|=U;if(p=p.next,p===null){if(p=i.shared.pending,p===null)break;L=p,p=L.next,L.next=null,i.lastBaseUpdate=L,i.shared.pending=null}}while(!0);q===null&&(j=V),i.baseState=j,i.firstBaseUpdate=D,i.lastBaseUpdate=q,r===null&&(i.shared.lanes=0),zl|=d,e.lanes=d,e.memoizedState=V}}function bf(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function jf(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)bf(l[e],t)}var Ua=G(null),Ji=G(0);function Nf(e,t){e=ul,K(Ji,e),K(Ua,t),ul=e|t.baseLanes}function Wr(){K(Ji,ul),K(Ua,Ua.current)}function Ir(){ul=Ji.current,F(Ua),F(Ji)}var Sl=0,pe=null,Ae=null,Xe=null,Qi=!1,La=!1,aa=!1,ki=0,On=0,Ha=null,J1=0;function Ye(){throw Error(u(321))}function ec(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!xt(e[l],t[l]))return!1;return!0}function tc(e,t,l,a,i,r){return Sl=r,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,_.H=e===null||e.memoizedState===null?id:sd,aa=!1,r=l(a,i),aa=!1,La&&(r=Ef(t,l,a,i)),Sf(e),r}function Sf(e){_.H=Wi;var t=Ae!==null&&Ae.next!==null;if(Sl=0,Xe=Ae=pe=null,Qi=!1,On=0,Ha=null,t)throw Error(u(300));e===null||Ke||(e=e.dependencies,e!==null&&qi(e)&&(Ke=!0))}function Ef(e,t,l,a){pe=e;var i=0;do{if(La&&(Ha=null),On=0,La=!1,25<=i)throw Error(u(301));if(i+=1,Xe=Ae=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}_.H=P1,r=t(l,a)}while(La);return r}function Q1(){var e=_.H,t=e.useState()[0];return t=typeof t.then=="function"?Dn(t):t,e=e.useState()[0],(Ae!==null?Ae.memoizedState:null)!==e&&(pe.flags|=1024),t}function lc(){var e=ki!==0;return ki=0,e}function ac(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function nc(e){if(Qi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Qi=!1}Sl=0,Xe=Ae=pe=null,La=!1,On=ki=0,Ha=null}function ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?pe.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function Je(){if(Ae===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ae.next;var t=Xe===null?pe.memoizedState:Xe.next;if(t!==null)Xe=t,Ae=e;else{if(e===null)throw pe.alternate===null?Error(u(467)):Error(u(310));Ae=e,e={memoizedState:Ae.memoizedState,baseState:Ae.baseState,baseQueue:Ae.baseQueue,queue:Ae.queue,next:null},Xe===null?pe.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function ic(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Dn(e){var t=On;return On+=1,Ha===null&&(Ha=[]),e=yf(Ha,e,t),t=pe,(Xe===null?t.memoizedState:Xe.next)===null&&(t=t.alternate,_.H=t===null||t.memoizedState===null?id:sd),e}function Zi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Dn(e);if(e.$$typeof===J)return tt(e)}throw Error(u(438,String(e)))}function sc(e){var t=null,l=pe.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=pe.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=ic(),pe.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=ze;return t.index++,l}function al(e,t){return typeof t=="function"?t(e):t}function Ki(e){var t=Je();return rc(t,Ae,e)}function rc(e,t,l){var a=e.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=l;var i=e.baseQueue,r=a.pending;if(r!==null){if(i!==null){var d=i.next;i.next=r.next,r.next=d}t.baseQueue=i=r,a.pending=null}if(r=e.baseState,i===null)e.memoizedState=r;else{t=i.next;var p=d=null,j=null,D=t,q=!1;do{var V=D.lane&-536870913;if(V!==D.lane?(be&V)===V:(Sl&V)===V){var U=D.revertLane;if(U===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),V===Ma&&(q=!0);else if((Sl&U)===U){D=D.next,U===Ma&&(q=!0);continue}else V={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},j===null?(p=j=V,d=r):j=j.next=V,pe.lanes|=U,zl|=U;V=D.action,aa&&l(r,V),r=D.hasEagerState?D.eagerState:l(r,V)}else U={lane:V,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},j===null?(p=j=U,d=r):j=j.next=U,pe.lanes|=V,zl|=V;D=D.next}while(D!==null&&D!==t);if(j===null?d=r:j.next=p,!xt(r,e.memoizedState)&&(Ke=!0,q&&(l=_a,l!==null)))throw l;e.memoizedState=r,e.baseState=d,e.baseQueue=j,a.lastRenderedState=r}return i===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function cc(e){var t=Je(),l=t.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=e;var a=l.dispatch,i=l.pending,r=t.memoizedState;if(i!==null){l.pending=null;var d=i=i.next;do r=e(r,d.action),d=d.next;while(d!==i);xt(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),l.lastRenderedState=r}return[r,a]}function wf(e,t,l){var a=pe,i=Je(),r=Se;if(r){if(l===void 0)throw Error(u(407));l=l()}else l=t();var d=!xt((Ae||i).memoizedState,l);d&&(i.memoizedState=l,Ke=!0),i=i.queue;var p=Rf.bind(null,a,i,e);if(Mn(2048,8,p,[e]),i.getSnapshot!==t||d||Xe!==null&&Xe.memoizedState.tag&1){if(a.flags|=2048,Ba(9,Fi(),Tf.bind(null,a,i,l,t),null),Oe===null)throw Error(u(349));r||(Sl&124)!==0||Af(a,t,l)}return l}function Af(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=pe.updateQueue,t===null?(t=ic(),pe.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Tf(e,t,l,a){t.value=l,t.getSnapshot=a,Cf(t)&&zf(e)}function Rf(e,t,l){return l(function(){Cf(t)&&zf(e)})}function Cf(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!xt(e,l)}catch{return!0}}function zf(e){var t=Ca(e,2);t!==null&&Nt(t,e,2)}function uc(e){var t=ct();if(typeof e=="function"){var l=e;if(e=l(),aa){xl(!0);try{l()}finally{xl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:e},t}function Of(e,t,l,a){return e.baseState=l,rc(e,Ae,typeof a=="function"?a:al)}function k1(e,t,l,a,i){if(Pi(e))throw Error(u(485));if(e=t.action,e!==null){var r={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){r.listeners.push(d)}};_.T!==null?l(!0):r.isTransition=!1,a(r),l=t.pending,l===null?(r.next=t.pending=r,Df(t,r)):(r.next=l.next,t.pending=l.next=r)}}function Df(e,t){var l=t.action,a=t.payload,i=e.state;if(t.isTransition){var r=_.T,d={};_.T=d;try{var p=l(i,a),j=_.S;j!==null&&j(d,p),Mf(e,t,p)}catch(D){oc(e,t,D)}finally{_.T=r}}else try{r=l(i,a),Mf(e,t,r)}catch(D){oc(e,t,D)}}function Mf(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){_f(e,t,a)},function(a){return oc(e,t,a)}):_f(e,t,l)}function _f(e,t,l){t.status="fulfilled",t.value=l,Uf(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Df(e,l)))}function oc(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,Uf(t),t=t.next;while(t!==a)}e.action=null}function Uf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Lf(e,t){return t}function Hf(e,t){if(Se){var l=Oe.formState;if(l!==null){e:{var a=pe;if(Se){if(Be){t:{for(var i=Be,r=Jt;i.nodeType!==8;){if(!r){i=null;break t}if(i=Bt(i.nextSibling),i===null){i=null;break t}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Be=Bt(i.nextSibling),a=i.data==="F!";break e}}Il(a)}a=!1}a&&(t=l[0])}}return l=ct(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Lf,lastRenderedState:t},l.queue=a,l=ld.bind(null,pe,a),a.dispatch=l,a=uc(!1),r=pc.bind(null,pe,!1,a.queue),a=ct(),i={state:t,dispatch:null,action:e,pending:null},a.queue=i,l=k1.bind(null,pe,i,r,l),i.dispatch=l,a.memoizedState=e,[t,l,!1]}function Bf(e){var t=Je();return qf(t,Ae,e)}function qf(e,t,l){if(t=rc(e,t,Lf)[0],e=Ki(al)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Dn(t)}catch(d){throw d===An?Gi:d}else a=t;t=Je();var i=t.queue,r=i.dispatch;return l!==t.memoizedState&&(pe.flags|=2048,Ba(9,Fi(),Z1.bind(null,i,l),null)),[a,r,e]}function Z1(e,t){e.action=t}function Yf(e){var t=Je(),l=Ae;if(l!==null)return qf(t,l,e);Je(),t=t.memoizedState,l=Je();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Ba(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=pe.updateQueue,t===null&&(t=ic(),pe.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Fi(){return{destroy:void 0,resource:void 0}}function Vf(){return Je().memoizedState}function $i(e,t,l,a){var i=ct();a=a===void 0?null:a,pe.flags|=e,i.memoizedState=Ba(1|t,Fi(),l,a)}function Mn(e,t,l,a){var i=Je();a=a===void 0?null:a;var r=i.memoizedState.inst;Ae!==null&&a!==null&&ec(a,Ae.memoizedState.deps)?i.memoizedState=Ba(t,r,l,a):(pe.flags|=e,i.memoizedState=Ba(1|t,r,l,a))}function Gf(e,t){$i(8390656,8,e,t)}function Xf(e,t){Mn(2048,8,e,t)}function Jf(e,t){return Mn(4,2,e,t)}function Qf(e,t){return Mn(4,4,e,t)}function kf(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Zf(e,t,l){l=l!=null?l.concat([e]):null,Mn(4,4,kf.bind(null,t,e),l)}function fc(){}function Kf(e,t){var l=Je();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&ec(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Ff(e,t){var l=Je();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&ec(t,a[1]))return a[0];if(a=e(),aa){xl(!0);try{e()}finally{xl(!1)}}return l.memoizedState=[a,t],a}function dc(e,t,l){return l===void 0||(Sl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Wd(),pe.lanes|=e,zl|=e,l)}function $f(e,t,l,a){return xt(l,t)?l:Ua.current!==null?(e=dc(e,l,a),xt(e,t)||(Ke=!0),e):(Sl&42)===0?(Ke=!0,e.memoizedState=l):(e=Wd(),pe.lanes|=e,zl|=e,t)}function Pf(e,t,l,a,i){var r=X.p;X.p=r!==0&&8>r?r:8;var d=_.T,p={};_.T=p,pc(e,!1,t,l);try{var j=i(),D=_.S;if(D!==null&&D(p,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var q=X1(j,a);_n(e,t,q,jt(e))}else _n(e,t,a,jt(e))}catch(V){_n(e,t,{then:function(){},status:"rejected",reason:V},jt())}finally{X.p=r,_.T=d}}function K1(){}function mc(e,t,l,a){if(e.tag!==5)throw Error(u(476));var i=Wf(e).queue;Pf(e,i,t,P,l===null?K1:function(){return If(e),l(a)})}function Wf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:P},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function If(e){var t=Wf(e).next.queue;_n(e,t,{},jt())}function hc(){return tt(Wn)}function ed(){return Je().memoizedState}function td(){return Je().memoizedState}function F1(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=jt();e=jl(l);var a=Nl(t,e,l);a!==null&&(Nt(a,t,l),Rn(a,t,l)),t={cache:Jr()},e.payload=t;return}t=t.return}}function $1(e,t,l){var a=jt();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Pi(e)?ad(t,l):(l=_r(e,t,l,a),l!==null&&(Nt(l,e,a),nd(l,t,a)))}function ld(e,t,l){var a=jt();_n(e,t,l,a)}function _n(e,t,l,a){var i={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Pi(e))ad(t,i);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var d=t.lastRenderedState,p=r(d,l);if(i.hasEagerState=!0,i.eagerState=p,xt(p,d))return _i(e,t,i,0),Oe===null&&Mi(),!1}catch{}finally{}if(l=_r(e,t,i,a),l!==null)return Nt(l,e,a),nd(l,t,a),!0}return!1}function pc(e,t,l,a){if(a={lane:2,revertLane:Zc(),action:a,hasEagerState:!1,eagerState:null,next:null},Pi(e)){if(t)throw Error(u(479))}else t=_r(e,l,a,2),t!==null&&Nt(t,e,2)}function Pi(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function ad(e,t){La=Qi=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function nd(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,oo(e,l)}}var Wi={readContext:tt,use:Zi,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useLayoutEffect:Ye,useInsertionEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useSyncExternalStore:Ye,useId:Ye,useHostTransitionStatus:Ye,useFormState:Ye,useActionState:Ye,useOptimistic:Ye,useMemoCache:Ye,useCacheRefresh:Ye},id={readContext:tt,use:Zi,useCallback:function(e,t){return ct().memoizedState=[e,t===void 0?null:t],e},useContext:tt,useEffect:Gf,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,$i(4194308,4,kf.bind(null,t,e),l)},useLayoutEffect:function(e,t){return $i(4194308,4,e,t)},useInsertionEffect:function(e,t){$i(4,2,e,t)},useMemo:function(e,t){var l=ct();t=t===void 0?null:t;var a=e();if(aa){xl(!0);try{e()}finally{xl(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=ct();if(l!==void 0){var i=l(t);if(aa){xl(!0);try{l(t)}finally{xl(!1)}}}else i=t;return a.memoizedState=a.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},a.queue=e,e=e.dispatch=$1.bind(null,pe,e),[a.memoizedState,e]},useRef:function(e){var t=ct();return e={current:e},t.memoizedState=e},useState:function(e){e=uc(e);var t=e.queue,l=ld.bind(null,pe,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:fc,useDeferredValue:function(e,t){var l=ct();return dc(l,e,t)},useTransition:function(){var e=uc(!1);return e=Pf.bind(null,pe,e.queue,!0,!1),ct().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=pe,i=ct();if(Se){if(l===void 0)throw Error(u(407));l=l()}else{if(l=t(),Oe===null)throw Error(u(349));(be&124)!==0||Af(a,t,l)}i.memoizedState=l;var r={value:l,getSnapshot:t};return i.queue=r,Gf(Rf.bind(null,a,r,e),[e]),a.flags|=2048,Ba(9,Fi(),Tf.bind(null,a,r,l,t),null),l},useId:function(){var e=ct(),t=Oe.identifierPrefix;if(Se){var l=el,a=It;l=(a&~(1<<32-pt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=ki++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=J1++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:hc,useFormState:Hf,useActionState:Hf,useOptimistic:function(e){var t=ct();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=pc.bind(null,pe,!0,l),l.dispatch=t,[e,t]},useMemoCache:sc,useCacheRefresh:function(){return ct().memoizedState=F1.bind(null,pe)}},sd={readContext:tt,use:Zi,useCallback:Kf,useContext:tt,useEffect:Xf,useImperativeHandle:Zf,useInsertionEffect:Jf,useLayoutEffect:Qf,useMemo:Ff,useReducer:Ki,useRef:Vf,useState:function(){return Ki(al)},useDebugValue:fc,useDeferredValue:function(e,t){var l=Je();return $f(l,Ae.memoizedState,e,t)},useTransition:function(){var e=Ki(al)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Dn(e),t]},useSyncExternalStore:wf,useId:ed,useHostTransitionStatus:hc,useFormState:Bf,useActionState:Bf,useOptimistic:function(e,t){var l=Je();return Of(l,Ae,e,t)},useMemoCache:sc,useCacheRefresh:td},P1={readContext:tt,use:Zi,useCallback:Kf,useContext:tt,useEffect:Xf,useImperativeHandle:Zf,useInsertionEffect:Jf,useLayoutEffect:Qf,useMemo:Ff,useReducer:cc,useRef:Vf,useState:function(){return cc(al)},useDebugValue:fc,useDeferredValue:function(e,t){var l=Je();return Ae===null?dc(l,e,t):$f(l,Ae.memoizedState,e,t)},useTransition:function(){var e=cc(al)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Dn(e),t]},useSyncExternalStore:wf,useId:ed,useHostTransitionStatus:hc,useFormState:Yf,useActionState:Yf,useOptimistic:function(e,t){var l=Je();return Ae!==null?Of(l,Ae,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:sc,useCacheRefresh:td},qa=null,Un=0;function Ii(e){var t=Un;return Un+=1,qa===null&&(qa=[]),yf(qa,e,t)}function Ln(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function es(e,t){throw t.$$typeof===N?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function rd(e){var t=e._init;return t(e._payload)}function cd(e){function t(C,A){if(e){var O=C.deletions;O===null?(C.deletions=[A],C.flags|=16):O.push(A)}}function l(C,A){if(!e)return null;for(;A!==null;)t(C,A),A=A.sibling;return null}function a(C){for(var A=new Map;C!==null;)C.key!==null?A.set(C.key,C):A.set(C.index,C),C=C.sibling;return A}function i(C,A){return C=Wt(C,A),C.index=0,C.sibling=null,C}function r(C,A,O){return C.index=O,e?(O=C.alternate,O!==null?(O=O.index,O<A?(C.flags|=67108866,A):O):(C.flags|=67108866,A)):(C.flags|=1048576,A)}function d(C){return e&&C.alternate===null&&(C.flags|=67108866),C}function p(C,A,O,Y){return A===null||A.tag!==6?(A=Lr(O,C.mode,Y),A.return=C,A):(A=i(A,O),A.return=C,A)}function j(C,A,O,Y){var $=O.type;return $===b?q(C,A,O.props.children,Y,O.key):A!==null&&(A.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===xe&&rd($)===A.type)?(A=i(A,O.props),Ln(A,O),A.return=C,A):(A=Li(O.type,O.key,O.props,null,C.mode,Y),Ln(A,O),A.return=C,A)}function D(C,A,O,Y){return A===null||A.tag!==4||A.stateNode.containerInfo!==O.containerInfo||A.stateNode.implementation!==O.implementation?(A=Hr(O,C.mode,Y),A.return=C,A):(A=i(A,O.children||[]),A.return=C,A)}function q(C,A,O,Y,$){return A===null||A.tag!==7?(A=Fl(O,C.mode,Y,$),A.return=C,A):(A=i(A,O),A.return=C,A)}function V(C,A,O){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=Lr(""+A,C.mode,O),A.return=C,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case S:return O=Li(A.type,A.key,A.props,null,C.mode,O),Ln(O,A),O.return=C,O;case H:return A=Hr(A,C.mode,O),A.return=C,A;case xe:var Y=A._init;return A=Y(A._payload),V(C,A,O)}if(W(A)||ae(A))return A=Fl(A,C.mode,O,null),A.return=C,A;if(typeof A.then=="function")return V(C,Ii(A),O);if(A.$$typeof===J)return V(C,Yi(C,A),O);es(C,A)}return null}function U(C,A,O,Y){var $=A!==null?A.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return $!==null?null:p(C,A,""+O,Y);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case S:return O.key===$?j(C,A,O,Y):null;case H:return O.key===$?D(C,A,O,Y):null;case xe:return $=O._init,O=$(O._payload),U(C,A,O,Y)}if(W(O)||ae(O))return $!==null?null:q(C,A,O,Y,null);if(typeof O.then=="function")return U(C,A,Ii(O),Y);if(O.$$typeof===J)return U(C,A,Yi(C,O),Y);es(C,O)}return null}function L(C,A,O,Y,$){if(typeof Y=="string"&&Y!==""||typeof Y=="number"||typeof Y=="bigint")return C=C.get(O)||null,p(A,C,""+Y,$);if(typeof Y=="object"&&Y!==null){switch(Y.$$typeof){case S:return C=C.get(Y.key===null?O:Y.key)||null,j(A,C,Y,$);case H:return C=C.get(Y.key===null?O:Y.key)||null,D(A,C,Y,$);case xe:var ye=Y._init;return Y=ye(Y._payload),L(C,A,O,Y,$)}if(W(Y)||ae(Y))return C=C.get(O)||null,q(A,C,Y,$,null);if(typeof Y.then=="function")return L(C,A,O,Ii(Y),$);if(Y.$$typeof===J)return L(C,A,O,Yi(A,Y),$);es(A,Y)}return null}function ce(C,A,O,Y){for(var $=null,ye=null,I=A,se=A=0,$e=null;I!==null&&se<O.length;se++){I.index>se?($e=I,I=null):$e=I.sibling;var Ne=U(C,I,O[se],Y);if(Ne===null){I===null&&(I=$e);break}e&&I&&Ne.alternate===null&&t(C,I),A=r(Ne,A,se),ye===null?$=Ne:ye.sibling=Ne,ye=Ne,I=$e}if(se===O.length)return l(C,I),Se&&Pl(C,se),$;if(I===null){for(;se<O.length;se++)I=V(C,O[se],Y),I!==null&&(A=r(I,A,se),ye===null?$=I:ye.sibling=I,ye=I);return Se&&Pl(C,se),$}for(I=a(I);se<O.length;se++)$e=L(I,C,se,O[se],Y),$e!==null&&(e&&$e.alternate!==null&&I.delete($e.key===null?se:$e.key),A=r($e,A,se),ye===null?$=$e:ye.sibling=$e,ye=$e);return e&&I.forEach(function(ql){return t(C,ql)}),Se&&Pl(C,se),$}function ie(C,A,O,Y){if(O==null)throw Error(u(151));for(var $=null,ye=null,I=A,se=A=0,$e=null,Ne=O.next();I!==null&&!Ne.done;se++,Ne=O.next()){I.index>se?($e=I,I=null):$e=I.sibling;var ql=U(C,I,Ne.value,Y);if(ql===null){I===null&&(I=$e);break}e&&I&&ql.alternate===null&&t(C,I),A=r(ql,A,se),ye===null?$=ql:ye.sibling=ql,ye=ql,I=$e}if(Ne.done)return l(C,I),Se&&Pl(C,se),$;if(I===null){for(;!Ne.done;se++,Ne=O.next())Ne=V(C,Ne.value,Y),Ne!==null&&(A=r(Ne,A,se),ye===null?$=Ne:ye.sibling=Ne,ye=Ne);return Se&&Pl(C,se),$}for(I=a(I);!Ne.done;se++,Ne=O.next())Ne=L(I,C,se,Ne.value,Y),Ne!==null&&(e&&Ne.alternate!==null&&I.delete(Ne.key===null?se:Ne.key),A=r(Ne,A,se),ye===null?$=Ne:ye.sibling=Ne,ye=Ne);return e&&I.forEach(function(W2){return t(C,W2)}),Se&&Pl(C,se),$}function Re(C,A,O,Y){if(typeof O=="object"&&O!==null&&O.type===b&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case S:e:{for(var $=O.key;A!==null;){if(A.key===$){if($=O.type,$===b){if(A.tag===7){l(C,A.sibling),Y=i(A,O.props.children),Y.return=C,C=Y;break e}}else if(A.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===xe&&rd($)===A.type){l(C,A.sibling),Y=i(A,O.props),Ln(Y,O),Y.return=C,C=Y;break e}l(C,A);break}else t(C,A);A=A.sibling}O.type===b?(Y=Fl(O.props.children,C.mode,Y,O.key),Y.return=C,C=Y):(Y=Li(O.type,O.key,O.props,null,C.mode,Y),Ln(Y,O),Y.return=C,C=Y)}return d(C);case H:e:{for($=O.key;A!==null;){if(A.key===$)if(A.tag===4&&A.stateNode.containerInfo===O.containerInfo&&A.stateNode.implementation===O.implementation){l(C,A.sibling),Y=i(A,O.children||[]),Y.return=C,C=Y;break e}else{l(C,A);break}else t(C,A);A=A.sibling}Y=Hr(O,C.mode,Y),Y.return=C,C=Y}return d(C);case xe:return $=O._init,O=$(O._payload),Re(C,A,O,Y)}if(W(O))return ce(C,A,O,Y);if(ae(O)){if($=ae(O),typeof $!="function")throw Error(u(150));return O=$.call(O),ie(C,A,O,Y)}if(typeof O.then=="function")return Re(C,A,Ii(O),Y);if(O.$$typeof===J)return Re(C,A,Yi(C,O),Y);es(C,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,A!==null&&A.tag===6?(l(C,A.sibling),Y=i(A,O),Y.return=C,C=Y):(l(C,A),Y=Lr(O,C.mode,Y),Y.return=C,C=Y),d(C)):l(C,A)}return function(C,A,O,Y){try{Un=0;var $=Re(C,A,O,Y);return qa=null,$}catch(I){if(I===An||I===Gi)throw I;var ye=yt(29,I,null,C.mode);return ye.lanes=Y,ye.return=C,ye}finally{}}}var Ya=cd(!0),ud=cd(!1),Dt=G(null),Qt=null;function El(e){var t=e.alternate;K(ke,ke.current&1),K(Dt,e),Qt===null&&(t===null||Ua.current!==null||t.memoizedState!==null)&&(Qt=e)}function od(e){if(e.tag===22){if(K(ke,ke.current),K(Dt,e),Qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Qt=e)}}else wl()}function wl(){K(ke,ke.current),K(Dt,Dt.current)}function nl(e){F(Dt),Qt===e&&(Qt=null),F(ke)}var ke=G(0);function ts(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||iu(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function xc(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:v({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var yc={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=jt(),i=jl(a);i.payload=t,l!=null&&(i.callback=l),t=Nl(e,i,a),t!==null&&(Nt(t,e,a),Rn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=jt(),i=jl(a);i.tag=1,i.payload=t,l!=null&&(i.callback=l),t=Nl(e,i,a),t!==null&&(Nt(t,e,a),Rn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=jt(),a=jl(l);a.tag=2,t!=null&&(a.callback=t),t=Nl(e,a,l),t!==null&&(Nt(t,e,l),Rn(t,e,l))}};function fd(e,t,l,a,i,r,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,r,d):t.prototype&&t.prototype.isPureReactComponent?!gn(l,a)||!gn(i,r):!0}function dd(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&yc.enqueueReplaceState(t,t.state,null)}function na(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=v({},l));for(var i in e)l[i]===void 0&&(l[i]=e[i])}return l}var ls=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function md(e){ls(e)}function hd(e){console.error(e)}function pd(e){ls(e)}function as(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function xd(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function gc(e,t,l){return l=jl(l),l.tag=3,l.payload={element:null},l.callback=function(){as(e,t)},l}function yd(e){return e=jl(e),e.tag=3,e}function gd(e,t,l,a){var i=l.type.getDerivedStateFromError;if(typeof i=="function"){var r=a.value;e.payload=function(){return i(r)},e.callback=function(){xd(t,l,a)}}var d=l.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){xd(t,l,a),typeof i!="function"&&(Ol===null?Ol=new Set([this]):Ol.add(this));var p=a.stack;this.componentDidCatch(a.value,{componentStack:p!==null?p:""})})}function W1(e,t,l,a,i){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&Sn(t,l,i,!0),l=Dt.current,l!==null){switch(l.tag){case 13:return Qt===null?Gc():l.alternate===null&&qe===0&&(qe=3),l.flags&=-257,l.flags|=65536,l.lanes=i,a===Zr?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Jc(e,a,i)),!1;case 22:return l.flags|=65536,a===Zr?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Jc(e,a,i)),!1}throw Error(u(435,l.tag))}return Jc(e,a,i),Gc(),!1}if(Se)return t=Dt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,a!==Yr&&(e=Error(u(422),{cause:a}),Nn(Rt(e,l)))):(a!==Yr&&(t=Error(u(423),{cause:a}),Nn(Rt(t,l))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,a=Rt(a,l),i=gc(e.stateNode,a,i),$r(e,i),qe!==4&&(qe=2)),!1;var r=Error(u(520),{cause:a});if(r=Rt(r,l),Xn===null?Xn=[r]:Xn.push(r),qe!==4&&(qe=2),t===null)return!0;a=Rt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=i&-i,l.lanes|=e,e=gc(l.stateNode,a,e),$r(l,e),!1;case 1:if(t=l.type,r=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Ol===null||!Ol.has(r))))return l.flags|=65536,i&=-i,l.lanes|=i,i=yd(i),gd(i,e,l,a),$r(l,i),!1}l=l.return}while(l!==null);return!1}var vd=Error(u(461)),Ke=!1;function Pe(e,t,l,a){t.child=e===null?ud(t,null,l,a):Ya(t,e.child,l,a)}function bd(e,t,l,a,i){l=l.render;var r=t.ref;if("ref"in a){var d={};for(var p in a)p!=="ref"&&(d[p]=a[p])}else d=a;return ta(t),a=tc(e,t,l,d,r,i),p=lc(),e!==null&&!Ke?(ac(e,t,i),il(e,t,i)):(Se&&p&&Br(t),t.flags|=1,Pe(e,t,a,i),t.child)}function jd(e,t,l,a,i){if(e===null){var r=l.type;return typeof r=="function"&&!Ur(r)&&r.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=r,Nd(e,t,r,a,i)):(e=Li(l.type,null,a,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!Ac(e,i)){var d=r.memoizedProps;if(l=l.compare,l=l!==null?l:gn,l(d,a)&&e.ref===t.ref)return il(e,t,i)}return t.flags|=1,e=Wt(r,a),e.ref=t.ref,e.return=t,t.child=e}function Nd(e,t,l,a,i){if(e!==null){var r=e.memoizedProps;if(gn(r,a)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=a=r,Ac(e,i))(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,il(e,t,i)}return vc(e,t,l,a,i)}function Sd(e,t,l){var a=t.pendingProps,i=a.children,r=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=r!==null?r.baseLanes|l:l,e!==null){for(i=t.child=e.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;t.childLanes=r&~a}else t.childLanes=0,t.child=null;return Ed(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Vi(t,r!==null?r.cachePool:null),r!==null?Nf(t,r):Wr(),od(t);else return t.lanes=t.childLanes=536870912,Ed(e,t,r!==null?r.baseLanes|l:l,l)}else r!==null?(Vi(t,r.cachePool),Nf(t,r),wl(),t.memoizedState=null):(e!==null&&Vi(t,null),Wr(),wl());return Pe(e,t,i,l),t.child}function Ed(e,t,l,a){var i=kr();return i=i===null?null:{parent:Qe._currentValue,pool:i},t.memoizedState={baseLanes:l,cachePool:i},e!==null&&Vi(t,null),Wr(),od(t),e!==null&&Sn(e,t,a,!0),null}function ns(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(u(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function vc(e,t,l,a,i){return ta(t),l=tc(e,t,l,a,void 0,i),a=lc(),e!==null&&!Ke?(ac(e,t,i),il(e,t,i)):(Se&&a&&Br(t),t.flags|=1,Pe(e,t,l,i),t.child)}function wd(e,t,l,a,i,r){return ta(t),t.updateQueue=null,l=Ef(t,a,l,i),Sf(e),a=lc(),e!==null&&!Ke?(ac(e,t,r),il(e,t,r)):(Se&&a&&Br(t),t.flags|=1,Pe(e,t,l,r),t.child)}function Ad(e,t,l,a,i){if(ta(t),t.stateNode===null){var r=za,d=l.contextType;typeof d=="object"&&d!==null&&(r=tt(d)),r=new l(a,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=yc,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=a,r.state=t.memoizedState,r.refs={},Kr(t),d=l.contextType,r.context=typeof d=="object"&&d!==null?tt(d):za,r.state=t.memoizedState,d=l.getDerivedStateFromProps,typeof d=="function"&&(xc(t,l,d,a),r.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(d=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),d!==r.state&&yc.enqueueReplaceState(r,r.state,null),zn(t,a,r,i),Cn(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){r=t.stateNode;var p=t.memoizedProps,j=na(l,p);r.props=j;var D=r.context,q=l.contextType;d=za,typeof q=="object"&&q!==null&&(d=tt(q));var V=l.getDerivedStateFromProps;q=typeof V=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,q||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||D!==d)&&dd(t,r,a,d),bl=!1;var U=t.memoizedState;r.state=U,zn(t,a,r,i),Cn(),D=t.memoizedState,p||U!==D||bl?(typeof V=="function"&&(xc(t,l,V,a),D=t.memoizedState),(j=bl||fd(t,l,j,a,U,D,d))?(q||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=D),r.props=a,r.state=D,r.context=d,a=j):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{r=t.stateNode,Fr(e,t),d=t.memoizedProps,q=na(l,d),r.props=q,V=t.pendingProps,U=r.context,D=l.contextType,j=za,typeof D=="object"&&D!==null&&(j=tt(D)),p=l.getDerivedStateFromProps,(D=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d!==V||U!==j)&&dd(t,r,a,j),bl=!1,U=t.memoizedState,r.state=U,zn(t,a,r,i),Cn();var L=t.memoizedState;d!==V||U!==L||bl||e!==null&&e.dependencies!==null&&qi(e.dependencies)?(typeof p=="function"&&(xc(t,l,p,a),L=t.memoizedState),(q=bl||fd(t,l,q,a,U,L,j)||e!==null&&e.dependencies!==null&&qi(e.dependencies))?(D||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(a,L,j),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(a,L,j)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=L),r.props=a,r.state=L,r.context=j,a=q):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),a=!1)}return r=a,ns(e,t),a=(t.flags&128)!==0,r||a?(r=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&a?(t.child=Ya(t,e.child,null,i),t.child=Ya(t,null,l,i)):Pe(e,t,l,i),t.memoizedState=r.state,e=t.child):e=il(e,t,i),e}function Td(e,t,l,a){return jn(),t.flags|=256,Pe(e,t,l,a),t.child}var bc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jc(e){return{baseLanes:e,cachePool:hf()}}function Nc(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Mt),e}function Rd(e,t,l){var a=t.pendingProps,i=!1,r=(t.flags&128)!==0,d;if((d=r)||(d=e!==null&&e.memoizedState===null?!1:(ke.current&2)!==0),d&&(i=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(i?El(t):wl(),Se){var p=Be,j;if(j=p){e:{for(j=p,p=Jt;j.nodeType!==8;){if(!p){p=null;break e}if(j=Bt(j.nextSibling),j===null){p=null;break e}}p=j}p!==null?(t.memoizedState={dehydrated:p,treeContext:$l!==null?{id:It,overflow:el}:null,retryLane:536870912,hydrationErrors:null},j=yt(18,null,null,0),j.stateNode=p,j.return=t,t.child=j,nt=t,Be=null,j=!0):j=!1}j||Il(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return iu(p)?t.lanes=32:t.lanes=536870912,null;nl(t)}return p=a.children,a=a.fallback,i?(wl(),i=t.mode,p=is({mode:"hidden",children:p},i),a=Fl(a,i,l,null),p.return=t,a.return=t,p.sibling=a,t.child=p,i=t.child,i.memoizedState=jc(l),i.childLanes=Nc(e,d,l),t.memoizedState=bc,a):(El(t),Sc(t,p))}if(j=e.memoizedState,j!==null&&(p=j.dehydrated,p!==null)){if(r)t.flags&256?(El(t),t.flags&=-257,t=Ec(e,t,l)):t.memoizedState!==null?(wl(),t.child=e.child,t.flags|=128,t=null):(wl(),i=a.fallback,p=t.mode,a=is({mode:"visible",children:a.children},p),i=Fl(i,p,l,null),i.flags|=2,a.return=t,i.return=t,a.sibling=i,t.child=a,Ya(t,e.child,null,l),a=t.child,a.memoizedState=jc(l),a.childLanes=Nc(e,d,l),t.memoizedState=bc,t=i);else if(El(t),iu(p)){if(d=p.nextSibling&&p.nextSibling.dataset,d)var D=d.dgst;d=D,a=Error(u(419)),a.stack="",a.digest=d,Nn({value:a,source:null,stack:null}),t=Ec(e,t,l)}else if(Ke||Sn(e,t,l,!1),d=(l&e.childLanes)!==0,Ke||d){if(d=Oe,d!==null&&(a=l&-l,a=(a&42)!==0?1:sr(a),a=(a&(d.suspendedLanes|l))!==0?0:a,a!==0&&a!==j.retryLane))throw j.retryLane=a,Ca(e,a),Nt(d,e,a),vd;p.data==="$?"||Gc(),t=Ec(e,t,l)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,Be=Bt(p.nextSibling),nt=t,Se=!0,Wl=null,Jt=!1,e!==null&&(zt[Ot++]=It,zt[Ot++]=el,zt[Ot++]=$l,It=e.id,el=e.overflow,$l=t),t=Sc(t,a.children),t.flags|=4096);return t}return i?(wl(),i=a.fallback,p=t.mode,j=e.child,D=j.sibling,a=Wt(j,{mode:"hidden",children:a.children}),a.subtreeFlags=j.subtreeFlags&65011712,D!==null?i=Wt(D,i):(i=Fl(i,p,l,null),i.flags|=2),i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,p=e.child.memoizedState,p===null?p=jc(l):(j=p.cachePool,j!==null?(D=Qe._currentValue,j=j.parent!==D?{parent:D,pool:D}:j):j=hf(),p={baseLanes:p.baseLanes|l,cachePool:j}),i.memoizedState=p,i.childLanes=Nc(e,d,l),t.memoizedState=bc,a):(El(t),l=e.child,e=l.sibling,l=Wt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=l,t.memoizedState=null,l)}function Sc(e,t){return t=is({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function is(e,t){return e=yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ec(e,t,l){return Ya(t,e.child,null,l),e=Sc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Cd(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Gr(e.return,t,l)}function wc(e,t,l,a,i){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:i}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=a,r.tail=l,r.tailMode=i)}function zd(e,t,l){var a=t.pendingProps,i=a.revealOrder,r=a.tail;if(Pe(e,t,a.children,l),a=ke.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cd(e,l,t);else if(e.tag===19)Cd(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(K(ke,a),i){case"forwards":for(l=t.child,i=null;l!==null;)e=l.alternate,e!==null&&ts(e)===null&&(i=l),l=l.sibling;l=i,l===null?(i=t.child,t.child=null):(i=l.sibling,l.sibling=null),wc(t,!1,i,l,r);break;case"backwards":for(l=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ts(e)===null){t.child=i;break}e=i.sibling,i.sibling=l,l=i,i=e}wc(t,!0,l,null,r);break;case"together":wc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function il(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),zl|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Sn(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,l=Wt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Wt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Ac(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&qi(e)))}function I1(e,t,l){switch(t.tag){case 3:_e(t,t.stateNode.containerInfo),vl(t,Qe,e.memoizedState.cache),jn();break;case 27:case 5:tr(t);break;case 4:_e(t,t.stateNode.containerInfo);break;case 10:vl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(El(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Rd(e,t,l):(El(t),e=il(e,t,l),e!==null?e.sibling:null);El(t);break;case 19:var i=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(Sn(e,t,l,!1),a=(l&t.childLanes)!==0),i){if(a)return zd(e,t,l);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),K(ke,ke.current),a)break;return null;case 22:case 23:return t.lanes=0,Sd(e,t,l);case 24:vl(t,Qe,e.memoizedState.cache)}return il(e,t,l)}function Od(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ke=!0;else{if(!Ac(e,l)&&(t.flags&128)===0)return Ke=!1,I1(e,t,l);Ke=(e.flags&131072)!==0}else Ke=!1,Se&&(t.flags&1048576)!==0&&rf(t,Bi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,i=a._init;if(a=i(a._payload),t.type=a,typeof a=="function")Ur(a)?(e=na(a,e),t.tag=1,t=Ad(null,t,a,e,l)):(t.tag=0,t=vc(null,t,a,e,l));else{if(a!=null){if(i=a.$$typeof,i===k){t.tag=11,t=bd(null,t,a,e,l);break e}else if(i===le){t.tag=14,t=jd(null,t,a,e,l);break e}}throw t=Me(a)||a,Error(u(306,t,""))}}return t;case 0:return vc(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,i=na(a,t.pendingProps),Ad(e,t,a,i,l);case 3:e:{if(_e(t,t.stateNode.containerInfo),e===null)throw Error(u(387));a=t.pendingProps;var r=t.memoizedState;i=r.element,Fr(e,t),zn(t,a,null,l);var d=t.memoizedState;if(a=d.cache,vl(t,Qe,a),a!==r.cache&&Xr(t,[Qe],l,!0),Cn(),a=d.element,r.isDehydrated)if(r={element:a,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=Td(e,t,a,l);break e}else if(a!==i){i=Rt(Error(u(424)),t),Nn(i),t=Td(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Be=Bt(e.firstChild),nt=t,Se=!0,Wl=null,Jt=!0,l=ud(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(jn(),a===i){t=il(e,t,l);break e}Pe(e,t,a,l)}t=t.child}return t;case 26:return ns(e,t),e===null?(l=Um(t.type,null,t.pendingProps,null))?t.memoizedState=l:Se||(l=t.type,e=t.pendingProps,a=vs(fe.current).createElement(l),a[et]=t,a[st]=e,Ie(a,l,e),Ze(a),t.stateNode=a):t.memoizedState=Um(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return tr(t),e===null&&Se&&(a=t.stateNode=Dm(t.type,t.pendingProps,fe.current),nt=t,Jt=!0,i=Be,_l(t.type)?(su=i,Be=Bt(a.firstChild)):Be=i),Pe(e,t,t.pendingProps.children,l),ns(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((i=a=Be)&&(a=T2(a,t.type,t.pendingProps,Jt),a!==null?(t.stateNode=a,nt=t,Be=Bt(a.firstChild),Jt=!1,i=!0):i=!1),i||Il(t)),tr(t),i=t.type,r=t.pendingProps,d=e!==null?e.memoizedProps:null,a=r.children,lu(i,r)?a=null:d!==null&&lu(i,d)&&(t.flags|=32),t.memoizedState!==null&&(i=tc(e,t,Q1,null,null,l),Wn._currentValue=i),ns(e,t),Pe(e,t,a,l),t.child;case 6:return e===null&&Se&&((e=l=Be)&&(l=R2(l,t.pendingProps,Jt),l!==null?(t.stateNode=l,nt=t,Be=null,e=!0):e=!1),e||Il(t)),null;case 13:return Rd(e,t,l);case 4:return _e(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ya(t,null,a,l):Pe(e,t,a,l),t.child;case 11:return bd(e,t,t.type,t.pendingProps,l);case 7:return Pe(e,t,t.pendingProps,l),t.child;case 8:return Pe(e,t,t.pendingProps.children,l),t.child;case 12:return Pe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,vl(t,t.type,a.value),Pe(e,t,a.children,l),t.child;case 9:return i=t.type._context,a=t.pendingProps.children,ta(t),i=tt(i),a=a(i),t.flags|=1,Pe(e,t,a,l),t.child;case 14:return jd(e,t,t.type,t.pendingProps,l);case 15:return Nd(e,t,t.type,t.pendingProps,l);case 19:return zd(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=is(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Wt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Sd(e,t,l);case 24:return ta(t),a=tt(Qe),e===null?(i=kr(),i===null&&(i=Oe,r=Jr(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=l),i=r),t.memoizedState={parent:a,cache:i},Kr(t),vl(t,Qe,i)):((e.lanes&l)!==0&&(Fr(e,t),zn(t,null,null,l),Cn()),i=e.memoizedState,r=t.memoizedState,i.parent!==a?(i={parent:a,cache:a},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),vl(t,Qe,a)):(a=r.cache,vl(t,Qe,a),a!==i.cache&&Xr(t,[Qe],l,!0))),Pe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function sl(e){e.flags|=4}function Dd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Ym(t)){if(t=Dt.current,t!==null&&((be&4194048)===be?Qt!==null:(be&62914560)!==be&&(be&536870912)===0||t!==Qt))throw Tn=Zr,pf;e.flags|=8192}}function ss(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?co():536870912,e.lanes|=t,Ja|=t)}function Hn(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function He(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var i=e.child;i!==null;)l|=i.lanes|i.childLanes,a|=i.subtreeFlags&65011712,a|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)l|=i.lanes|i.childLanes,a|=i.subtreeFlags,a|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function e2(e,t,l){var a=t.pendingProps;switch(qr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return He(t),null;case 1:return He(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ll(Qe),pl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(bn(t)?sl(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,of())),He(t),null;case 26:return l=t.memoizedState,e===null?(sl(t),l!==null?(He(t),Dd(t,l)):(He(t),t.flags&=-16777217)):l?l!==e.memoizedState?(sl(t),He(t),Dd(t,l)):(He(t),t.flags&=-16777217):(e.memoizedProps!==a&&sl(t),He(t),t.flags&=-16777217),null;case 27:yi(t),l=fe.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&sl(t);else{if(!a){if(t.stateNode===null)throw Error(u(166));return He(t),null}e=ne.current,bn(t)?cf(t):(e=Dm(i,a,l),t.stateNode=e,sl(t))}return He(t),null;case 5:if(yi(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&sl(t);else{if(!a){if(t.stateNode===null)throw Error(u(166));return He(t),null}if(e=ne.current,bn(t))cf(t);else{switch(i=vs(fe.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?i.createElement("select",{is:a.is}):i.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?i.createElement(l,{is:a.is}):i.createElement(l)}}e[et]=t,e[st]=a;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(Ie(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&sl(t)}}return He(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&sl(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(u(166));if(e=fe.current,bn(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,i=nt,i!==null)switch(i.tag){case 27:case 5:a=i.memoizedProps}e[et]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||wm(e.nodeValue,l)),e||Il(t)}else e=vs(e).createTextNode(a),e[et]=t,t.stateNode=e}return He(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=bn(t),a!==null&&a.dehydrated!==null){if(e===null){if(!i)throw Error(u(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(u(317));i[et]=t}else jn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;He(t),i=!1}else i=of(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(nl(t),t):(nl(t),null)}if(nl(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,i=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(i=a.alternate.memoizedState.cachePool.pool);var r=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(r=a.memoizedState.cachePool.pool),r!==i&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),ss(t,t.updateQueue),He(t),null;case 4:return pl(),e===null&&Pc(t.stateNode.containerInfo),He(t),null;case 10:return ll(t.type),He(t),null;case 19:if(F(ke),i=t.memoizedState,i===null)return He(t),null;if(a=(t.flags&128)!==0,r=i.rendering,r===null)if(a)Hn(i,!1);else{if(qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=ts(e),r!==null){for(t.flags|=128,Hn(i,!1),e=r.updateQueue,t.updateQueue=e,ss(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)sf(l,e),l=l.sibling;return K(ke,ke.current&1|2),t.child}e=e.sibling}i.tail!==null&&Xt()>us&&(t.flags|=128,a=!0,Hn(i,!1),t.lanes=4194304)}else{if(!a)if(e=ts(r),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,ss(t,e),Hn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!Se)return He(t),null}else 2*Xt()-i.renderingStartTime>us&&l!==536870912&&(t.flags|=128,a=!0,Hn(i,!1),t.lanes=4194304);i.isBackwards?(r.sibling=t.child,t.child=r):(e=i.last,e!==null?e.sibling=r:t.child=r,i.last=r)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Xt(),t.sibling=null,e=ke.current,K(ke,a?e&1|2:e&1),t):(He(t),null);case 22:case 23:return nl(t),Ir(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(He(t),t.subtreeFlags&6&&(t.flags|=8192)):He(t),l=t.updateQueue,l!==null&&ss(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&F(la),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),ll(Qe),He(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function t2(e,t){switch(qr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ll(Qe),pl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return yi(t),null;case 13:if(nl(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));jn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(ke),null;case 4:return pl(),null;case 10:return ll(t.type),null;case 22:case 23:return nl(t),Ir(),e!==null&&F(la),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ll(Qe),null;case 25:return null;default:return null}}function Md(e,t){switch(qr(t),t.tag){case 3:ll(Qe),pl();break;case 26:case 27:case 5:yi(t);break;case 4:pl();break;case 13:nl(t);break;case 19:F(ke);break;case 10:ll(t.type);break;case 22:case 23:nl(t),Ir(),e!==null&&F(la);break;case 24:ll(Qe)}}function Bn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var i=a.next;l=i;do{if((l.tag&e)===e){a=void 0;var r=l.create,d=l.inst;a=r(),d.destroy=a}l=l.next}while(l!==i)}}catch(p){Ce(t,t.return,p)}}function Al(e,t,l){try{var a=t.updateQueue,i=a!==null?a.lastEffect:null;if(i!==null){var r=i.next;a=r;do{if((a.tag&e)===e){var d=a.inst,p=d.destroy;if(p!==void 0){d.destroy=void 0,i=t;var j=l,D=p;try{D()}catch(q){Ce(i,j,q)}}}a=a.next}while(a!==r)}}catch(q){Ce(t,t.return,q)}}function _d(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{jf(t,l)}catch(a){Ce(e,e.return,a)}}}function Ud(e,t,l){l.props=na(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){Ce(e,t,a)}}function qn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(i){Ce(e,t,i)}}function kt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(i){Ce(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(i){Ce(e,t,i)}else l.current=null}function Ld(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(i){Ce(e,e.return,i)}}function Tc(e,t,l){try{var a=e.stateNode;N2(a,e.type,l,t),a[st]=t}catch(i){Ce(e,e.return,i)}}function Hd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&_l(e.type)||e.tag===4}function Rc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Hd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&_l(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Cc(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=gs));else if(a!==4&&(a===27&&_l(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Cc(e,t,l),e=e.sibling;e!==null;)Cc(e,t,l),e=e.sibling}function rs(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&_l(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(rs(e,t,l),e=e.sibling;e!==null;)rs(e,t,l),e=e.sibling}function Bd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Ie(t,a,l),t[et]=e,t[st]=l}catch(r){Ce(e,e.return,r)}}var rl=!1,Ve=!1,zc=!1,qd=typeof WeakSet=="function"?WeakSet:Set,Fe=null;function l2(e,t){if(e=e.containerInfo,eu=ws,e=Fo(e),Rr(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var i=a.anchorOffset,r=a.focusNode;a=a.focusOffset;try{l.nodeType,r.nodeType}catch{l=null;break e}var d=0,p=-1,j=-1,D=0,q=0,V=e,U=null;t:for(;;){for(var L;V!==l||i!==0&&V.nodeType!==3||(p=d+i),V!==r||a!==0&&V.nodeType!==3||(j=d+a),V.nodeType===3&&(d+=V.nodeValue.length),(L=V.firstChild)!==null;)U=V,V=L;for(;;){if(V===e)break t;if(U===l&&++D===i&&(p=d),U===r&&++q===a&&(j=d),(L=V.nextSibling)!==null)break;V=U,U=V.parentNode}V=L}l=p===-1||j===-1?null:{start:p,end:j}}else l=null}l=l||{start:0,end:0}}else l=null;for(tu={focusedElem:e,selectionRange:l},ws=!1,Fe=t;Fe!==null;)if(t=Fe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Fe=e;else for(;Fe!==null;){switch(t=Fe,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,l=t,i=r.memoizedProps,r=r.memoizedState,a=l.stateNode;try{var ce=na(l.type,i,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(ce,r),a.__reactInternalSnapshotBeforeUpdate=e}catch(ie){Ce(l,l.return,ie)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)nu(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":nu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,Fe=e;break}Fe=t.return}}function Yd(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Tl(e,l),a&4&&Bn(5,l);break;case 1:if(Tl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(d){Ce(l,l.return,d)}else{var i=na(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){Ce(l,l.return,d)}}a&64&&_d(l),a&512&&qn(l,l.return);break;case 3:if(Tl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{jf(e,t)}catch(d){Ce(l,l.return,d)}}break;case 27:t===null&&a&4&&Bd(l);case 26:case 5:Tl(e,l),t===null&&a&4&&Ld(l),a&512&&qn(l,l.return);break;case 12:Tl(e,l);break;case 13:Tl(e,l),a&4&&Xd(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=f2.bind(null,l),C2(e,l))));break;case 22:if(a=l.memoizedState!==null||rl,!a){t=t!==null&&t.memoizedState!==null||Ve,i=rl;var r=Ve;rl=a,(Ve=t)&&!r?Rl(e,l,(l.subtreeFlags&8772)!==0):Tl(e,l),rl=i,Ve=r}break;case 30:break;default:Tl(e,l)}}function Vd(e){var t=e.alternate;t!==null&&(e.alternate=null,Vd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ur(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ue=null,ut=!1;function cl(e,t,l){for(l=l.child;l!==null;)Gd(e,t,l),l=l.sibling}function Gd(e,t,l){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(sn,l)}catch{}switch(l.tag){case 26:Ve||kt(l,t),cl(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ve||kt(l,t);var a=Ue,i=ut;_l(l.type)&&(Ue=l.stateNode,ut=!1),cl(e,t,l),Kn(l.stateNode),Ue=a,ut=i;break;case 5:Ve||kt(l,t);case 6:if(a=Ue,i=ut,Ue=null,cl(e,t,l),Ue=a,ut=i,Ue!==null)if(ut)try{(Ue.nodeType===9?Ue.body:Ue.nodeName==="HTML"?Ue.ownerDocument.body:Ue).removeChild(l.stateNode)}catch(r){Ce(l,t,r)}else try{Ue.removeChild(l.stateNode)}catch(r){Ce(l,t,r)}break;case 18:Ue!==null&&(ut?(e=Ue,zm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),li(e)):zm(Ue,l.stateNode));break;case 4:a=Ue,i=ut,Ue=l.stateNode.containerInfo,ut=!0,cl(e,t,l),Ue=a,ut=i;break;case 0:case 11:case 14:case 15:Ve||Al(2,l,t),Ve||Al(4,l,t),cl(e,t,l);break;case 1:Ve||(kt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Ud(l,t,a)),cl(e,t,l);break;case 21:cl(e,t,l);break;case 22:Ve=(a=Ve)||l.memoizedState!==null,cl(e,t,l),Ve=a;break;default:cl(e,t,l)}}function Xd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{li(e)}catch(l){Ce(t,t.return,l)}}function a2(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new qd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new qd),t;default:throw Error(u(435,e.tag))}}function Oc(e,t){var l=a2(e);t.forEach(function(a){var i=d2.bind(null,e,a);l.has(a)||(l.add(a),a.then(i,i))})}function gt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var i=l[a],r=e,d=t,p=d;e:for(;p!==null;){switch(p.tag){case 27:if(_l(p.type)){Ue=p.stateNode,ut=!1;break e}break;case 5:Ue=p.stateNode,ut=!1;break e;case 3:case 4:Ue=p.stateNode.containerInfo,ut=!0;break e}p=p.return}if(Ue===null)throw Error(u(160));Gd(r,d,i),Ue=null,ut=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Jd(t,e),t=t.sibling}var Ht=null;function Jd(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:gt(t,e),vt(e),a&4&&(Al(3,e,e.return),Bn(3,e),Al(5,e,e.return));break;case 1:gt(t,e),vt(e),a&512&&(Ve||l===null||kt(l,l.return)),a&64&&rl&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var i=Ht;if(gt(t,e),vt(e),a&512&&(Ve||l===null||kt(l,l.return)),a&4){var r=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,i=i.ownerDocument||i;t:switch(a){case"title":r=i.getElementsByTagName("title")[0],(!r||r[un]||r[et]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(a),i.head.insertBefore(r,i.querySelector("head > title"))),Ie(r,a,l),r[et]=e,Ze(r),a=r;break e;case"link":var d=Bm("link","href",i).get(a+(l.href||""));if(d){for(var p=0;p<d.length;p++)if(r=d[p],r.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&r.getAttribute("rel")===(l.rel==null?null:l.rel)&&r.getAttribute("title")===(l.title==null?null:l.title)&&r.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){d.splice(p,1);break t}}r=i.createElement(a),Ie(r,a,l),i.head.appendChild(r);break;case"meta":if(d=Bm("meta","content",i).get(a+(l.content||""))){for(p=0;p<d.length;p++)if(r=d[p],r.getAttribute("content")===(l.content==null?null:""+l.content)&&r.getAttribute("name")===(l.name==null?null:l.name)&&r.getAttribute("property")===(l.property==null?null:l.property)&&r.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&r.getAttribute("charset")===(l.charSet==null?null:l.charSet)){d.splice(p,1);break t}}r=i.createElement(a),Ie(r,a,l),i.head.appendChild(r);break;default:throw Error(u(468,a))}r[et]=e,Ze(r),a=r}e.stateNode=a}else qm(i,e.type,e.stateNode);else e.stateNode=Hm(i,a,e.memoizedProps);else r!==a?(r===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):r.count--,a===null?qm(i,e.type,e.stateNode):Hm(i,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Tc(e,e.memoizedProps,l.memoizedProps)}break;case 27:gt(t,e),vt(e),a&512&&(Ve||l===null||kt(l,l.return)),l!==null&&a&4&&Tc(e,e.memoizedProps,l.memoizedProps);break;case 5:if(gt(t,e),vt(e),a&512&&(Ve||l===null||kt(l,l.return)),e.flags&32){i=e.stateNode;try{Na(i,"")}catch(L){Ce(e,e.return,L)}}a&4&&e.stateNode!=null&&(i=e.memoizedProps,Tc(e,i,l!==null?l.memoizedProps:i)),a&1024&&(zc=!0);break;case 6:if(gt(t,e),vt(e),a&4){if(e.stateNode===null)throw Error(u(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(L){Ce(e,e.return,L)}}break;case 3:if(Ns=null,i=Ht,Ht=bs(t.containerInfo),gt(t,e),Ht=i,vt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{li(t.containerInfo)}catch(L){Ce(e,e.return,L)}zc&&(zc=!1,Qd(e));break;case 4:a=Ht,Ht=bs(e.stateNode.containerInfo),gt(t,e),vt(e),Ht=a;break;case 12:gt(t,e),vt(e);break;case 13:gt(t,e),vt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hc=Xt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Oc(e,a)));break;case 22:i=e.memoizedState!==null;var j=l!==null&&l.memoizedState!==null,D=rl,q=Ve;if(rl=D||i,Ve=q||j,gt(t,e),Ve=q,rl=D,vt(e),a&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(l===null||j||rl||Ve||ia(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){j=l=t;try{if(r=j.stateNode,i)d=r.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{p=j.stateNode;var V=j.memoizedProps.style,U=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(L){Ce(j,j.return,L)}}}else if(t.tag===6){if(l===null){j=t;try{j.stateNode.nodeValue=i?"":j.memoizedProps}catch(L){Ce(j,j.return,L)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Oc(e,l))));break;case 19:gt(t,e),vt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Oc(e,a)));break;case 30:break;case 21:break;default:gt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(Hd(a)){l=a;break}a=a.return}if(l==null)throw Error(u(160));switch(l.tag){case 27:var i=l.stateNode,r=Rc(e);rs(e,r,i);break;case 5:var d=l.stateNode;l.flags&32&&(Na(d,""),l.flags&=-33);var p=Rc(e);rs(e,p,d);break;case 3:case 4:var j=l.stateNode.containerInfo,D=Rc(e);Cc(e,D,j);break;default:throw Error(u(161))}}catch(q){Ce(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Qd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Qd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Tl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Yd(e,t.alternate,t),t=t.sibling}function ia(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Al(4,t,t.return),ia(t);break;case 1:kt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Ud(t,t.return,l),ia(t);break;case 27:Kn(t.stateNode);case 26:case 5:kt(t,t.return),ia(t);break;case 22:t.memoizedState===null&&ia(t);break;case 30:ia(t);break;default:ia(t)}e=e.sibling}}function Rl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,i=e,r=t,d=r.flags;switch(r.tag){case 0:case 11:case 15:Rl(i,r,l),Bn(4,r);break;case 1:if(Rl(i,r,l),a=r,i=a.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(D){Ce(a,a.return,D)}if(a=r,i=a.updateQueue,i!==null){var p=a.stateNode;try{var j=i.shared.hiddenCallbacks;if(j!==null)for(i.shared.hiddenCallbacks=null,i=0;i<j.length;i++)bf(j[i],p)}catch(D){Ce(a,a.return,D)}}l&&d&64&&_d(r),qn(r,r.return);break;case 27:Bd(r);case 26:case 5:Rl(i,r,l),l&&a===null&&d&4&&Ld(r),qn(r,r.return);break;case 12:Rl(i,r,l);break;case 13:Rl(i,r,l),l&&d&4&&Xd(i,r);break;case 22:r.memoizedState===null&&Rl(i,r,l),qn(r,r.return);break;case 30:break;default:Rl(i,r,l)}t=t.sibling}}function Dc(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&En(l))}function Mc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&En(e))}function Zt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)kd(e,t,l,a),t=t.sibling}function kd(e,t,l,a){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,l,a),i&2048&&Bn(9,t);break;case 1:Zt(e,t,l,a);break;case 3:Zt(e,t,l,a),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&En(e)));break;case 12:if(i&2048){Zt(e,t,l,a),e=t.stateNode;try{var r=t.memoizedProps,d=r.id,p=r.onPostCommit;typeof p=="function"&&p(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Ce(t,t.return,j)}}else Zt(e,t,l,a);break;case 13:Zt(e,t,l,a);break;case 23:break;case 22:r=t.stateNode,d=t.alternate,t.memoizedState!==null?r._visibility&2?Zt(e,t,l,a):Yn(e,t):r._visibility&2?Zt(e,t,l,a):(r._visibility|=2,Va(e,t,l,a,(t.subtreeFlags&10256)!==0)),i&2048&&Dc(d,t);break;case 24:Zt(e,t,l,a),i&2048&&Mc(t.alternate,t);break;default:Zt(e,t,l,a)}}function Va(e,t,l,a,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,d=t,p=l,j=a,D=d.flags;switch(d.tag){case 0:case 11:case 15:Va(r,d,p,j,i),Bn(8,d);break;case 23:break;case 22:var q=d.stateNode;d.memoizedState!==null?q._visibility&2?Va(r,d,p,j,i):Yn(r,d):(q._visibility|=2,Va(r,d,p,j,i)),i&&D&2048&&Dc(d.alternate,d);break;case 24:Va(r,d,p,j,i),i&&D&2048&&Mc(d.alternate,d);break;default:Va(r,d,p,j,i)}t=t.sibling}}function Yn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,i=a.flags;switch(a.tag){case 22:Yn(l,a),i&2048&&Dc(a.alternate,a);break;case 24:Yn(l,a),i&2048&&Mc(a.alternate,a);break;default:Yn(l,a)}t=t.sibling}}var Vn=8192;function Ga(e){if(e.subtreeFlags&Vn)for(e=e.child;e!==null;)Zd(e),e=e.sibling}function Zd(e){switch(e.tag){case 26:Ga(e),e.flags&Vn&&e.memoizedState!==null&&G2(Ht,e.memoizedState,e.memoizedProps);break;case 5:Ga(e);break;case 3:case 4:var t=Ht;Ht=bs(e.stateNode.containerInfo),Ga(e),Ht=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Vn,Vn=16777216,Ga(e),Vn=t):Ga(e));break;default:Ga(e)}}function Kd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Gn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Fe=a,$d(a,e)}Kd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Fd(e),e=e.sibling}function Fd(e){switch(e.tag){case 0:case 11:case 15:Gn(e),e.flags&2048&&Al(9,e,e.return);break;case 3:Gn(e);break;case 12:Gn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,cs(e)):Gn(e);break;default:Gn(e)}}function cs(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Fe=a,$d(a,e)}Kd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Al(8,t,t.return),cs(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,cs(t));break;default:cs(t)}e=e.sibling}}function $d(e,t){for(;Fe!==null;){var l=Fe;switch(l.tag){case 0:case 11:case 15:Al(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:En(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Fe=a;else e:for(l=e;Fe!==null;){a=Fe;var i=a.sibling,r=a.return;if(Vd(a),a===l){Fe=null;break e}if(i!==null){i.return=r,Fe=i;break e}Fe=r}}}var n2={getCacheForType:function(e){var t=tt(Qe),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},i2=typeof WeakMap=="function"?WeakMap:Map,Ee=0,Oe=null,ge=null,be=0,we=0,bt=null,Cl=!1,Xa=!1,_c=!1,ul=0,qe=0,zl=0,sa=0,Uc=0,Mt=0,Ja=0,Xn=null,ot=null,Lc=!1,Hc=0,us=1/0,os=null,Ol=null,We=0,Dl=null,Qa=null,ka=0,Bc=0,qc=null,Pd=null,Jn=0,Yc=null;function jt(){if((Ee&2)!==0&&be!==0)return be&-be;if(_.T!==null){var e=Ma;return e!==0?e:Zc()}return fo()}function Wd(){Mt===0&&(Mt=(be&536870912)===0||Se?ro():536870912);var e=Dt.current;return e!==null&&(e.flags|=32),Mt}function Nt(e,t,l){(e===Oe&&(we===2||we===9)||e.cancelPendingCommit!==null)&&(Za(e,0),Ml(e,be,Mt,!1)),cn(e,l),((Ee&2)===0||e!==Oe)&&(e===Oe&&((Ee&2)===0&&(sa|=l),qe===4&&Ml(e,be,Mt,!1)),Kt(e))}function Id(e,t,l){if((Ee&6)!==0)throw Error(u(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||rn(e,t),i=a?c2(e,t):Xc(e,t,!0),r=a;do{if(i===0){Xa&&!a&&Ml(e,t,0,!1);break}else{if(l=e.current.alternate,r&&!s2(l)){i=Xc(e,t,!1),r=!1;continue}if(i===2){if(r=t,e.errorRecoveryDisabledLanes&r)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var p=e;i=Xn;var j=p.current.memoizedState.isDehydrated;if(j&&(Za(p,d).flags|=256),d=Xc(p,d,!1),d!==2){if(_c&&!j){p.errorRecoveryDisabledLanes|=r,sa|=r,i=4;break e}r=ot,ot=i,r!==null&&(ot===null?ot=r:ot.push.apply(ot,r))}i=d}if(r=!1,i!==2)continue}}if(i===1){Za(e,0),Ml(e,t,0,!0);break}e:{switch(a=e,r=i,r){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:Ml(a,t,Mt,!Cl);break e;case 2:ot=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(i=Hc+300-Xt(),10<i)){if(Ml(a,t,Mt,!Cl),ji(a,0,!0)!==0)break e;a.timeoutHandle=Rm(em.bind(null,a,l,ot,os,Lc,t,Mt,sa,Ja,Cl,r,2,-0,0),i);break e}em(a,l,ot,os,Lc,t,Mt,sa,Ja,Cl,r,0,-0,0)}}break}while(!0);Kt(e)}function em(e,t,l,a,i,r,d,p,j,D,q,V,U,L){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(Pn={stylesheets:null,count:0,unsuspend:V2},Zd(t),V=X2(),V!==null)){e.cancelPendingCommit=V(rm.bind(null,e,t,r,l,a,i,d,p,j,q,1,U,L)),Ml(e,r,d,!D);return}rm(e,t,r,l,a,i,d,p,j)}function s2(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var i=l[a],r=i.getSnapshot;i=i.value;try{if(!xt(r(),i))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ml(e,t,l,a){t&=~Uc,t&=~sa,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var i=t;0<i;){var r=31-pt(i),d=1<<r;a[r]=-1,i&=~d}l!==0&&uo(e,l,t)}function fs(){return(Ee&6)===0?(Qn(0),!1):!0}function Vc(){if(ge!==null){if(we===0)var e=ge.return;else e=ge,tl=ea=null,nc(e),qa=null,Un=0,e=ge;for(;e!==null;)Md(e.alternate,e),e=e.return;ge=null}}function Za(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,E2(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Vc(),Oe=e,ge=l=Wt(e.current,null),be=t,we=0,bt=null,Cl=!1,Xa=rn(e,t),_c=!1,Ja=Mt=Uc=sa=zl=qe=0,ot=Xn=null,Lc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var i=31-pt(a),r=1<<i;t|=e[i],a&=~r}return ul=t,Mi(),l}function tm(e,t){pe=null,_.H=Wi,t===An||t===Gi?(t=gf(),we=3):t===pf?(t=gf(),we=4):we=t===vd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,bt=t,ge===null&&(qe=1,as(e,Rt(t,e.current)))}function lm(){var e=_.H;return _.H=Wi,e===null?Wi:e}function am(){var e=_.A;return _.A=n2,e}function Gc(){qe=4,Cl||(be&4194048)!==be&&Dt.current!==null||(Xa=!0),(zl&134217727)===0&&(sa&134217727)===0||Oe===null||Ml(Oe,be,Mt,!1)}function Xc(e,t,l){var a=Ee;Ee|=2;var i=lm(),r=am();(Oe!==e||be!==t)&&(os=null,Za(e,t)),t=!1;var d=qe;e:do try{if(we!==0&&ge!==null){var p=ge,j=bt;switch(we){case 8:Vc(),d=6;break e;case 3:case 2:case 9:case 6:Dt.current===null&&(t=!0);var D=we;if(we=0,bt=null,Ka(e,p,j,D),l&&Xa){d=0;break e}break;default:D=we,we=0,bt=null,Ka(e,p,j,D)}}r2(),d=qe;break}catch(q){tm(e,q)}while(!0);return t&&e.shellSuspendCounter++,tl=ea=null,Ee=a,_.H=i,_.A=r,ge===null&&(Oe=null,be=0,Mi()),d}function r2(){for(;ge!==null;)nm(ge)}function c2(e,t){var l=Ee;Ee|=2;var a=lm(),i=am();Oe!==e||be!==t?(os=null,us=Xt()+500,Za(e,t)):Xa=rn(e,t);e:do try{if(we!==0&&ge!==null){t=ge;var r=bt;t:switch(we){case 1:we=0,bt=null,Ka(e,t,r,1);break;case 2:case 9:if(xf(r)){we=0,bt=null,im(t);break}t=function(){we!==2&&we!==9||Oe!==e||(we=7),Kt(e)},r.then(t,t);break e;case 3:we=7;break e;case 4:we=5;break e;case 7:xf(r)?(we=0,bt=null,im(t)):(we=0,bt=null,Ka(e,t,r,7));break;case 5:var d=null;switch(ge.tag){case 26:d=ge.memoizedState;case 5:case 27:var p=ge;if(!d||Ym(d)){we=0,bt=null;var j=p.sibling;if(j!==null)ge=j;else{var D=p.return;D!==null?(ge=D,ds(D)):ge=null}break t}}we=0,bt=null,Ka(e,t,r,5);break;case 6:we=0,bt=null,Ka(e,t,r,6);break;case 8:Vc(),qe=6;break e;default:throw Error(u(462))}}u2();break}catch(q){tm(e,q)}while(!0);return tl=ea=null,_.H=a,_.A=i,Ee=l,ge!==null?0:(Oe=null,be=0,Mi(),qe)}function u2(){for(;ge!==null&&!Oh();)nm(ge)}function nm(e){var t=Od(e.alternate,e,ul);e.memoizedProps=e.pendingProps,t===null?ds(e):ge=t}function im(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=wd(l,t,t.pendingProps,t.type,void 0,be);break;case 11:t=wd(l,t,t.pendingProps,t.type.render,t.ref,be);break;case 5:nc(t);default:Md(l,t),t=ge=sf(t,ul),t=Od(l,t,ul)}e.memoizedProps=e.pendingProps,t===null?ds(e):ge=t}function Ka(e,t,l,a){tl=ea=null,nc(t),qa=null,Un=0;var i=t.return;try{if(W1(e,i,t,l,be)){qe=1,as(e,Rt(l,e.current)),ge=null;return}}catch(r){if(i!==null)throw ge=i,r;qe=1,as(e,Rt(l,e.current)),ge=null;return}t.flags&32768?(Se||a===1?e=!0:Xa||(be&536870912)!==0?e=!1:(Cl=e=!0,(a===2||a===9||a===3||a===6)&&(a=Dt.current,a!==null&&a.tag===13&&(a.flags|=16384))),sm(t,e)):ds(t)}function ds(e){var t=e;do{if((t.flags&32768)!==0){sm(t,Cl);return}e=t.return;var l=e2(t.alternate,t,ul);if(l!==null){ge=l;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);qe===0&&(qe=5)}function sm(e,t){do{var l=t2(e.alternate,e);if(l!==null){l.flags&=32767,ge=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){ge=e;return}ge=e=l}while(e!==null);qe=6,ge=null}function rm(e,t,l,a,i,r,d,p,j){e.cancelPendingCommit=null;do ms();while(We!==0);if((Ee&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(r=t.lanes|t.childLanes,r|=Mr,Vh(e,l,r,d,p,j),e===Oe&&(ge=Oe=null,be=0),Qa=t,Dl=e,ka=l,Bc=r,qc=i,Pd=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,m2(gi,function(){return dm(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=_.T,_.T=null,i=X.p,X.p=2,d=Ee,Ee|=4;try{l2(e,t,l)}finally{Ee=d,X.p=i,_.T=a}}We=1,cm(),um(),om()}}function cm(){if(We===1){We=0;var e=Dl,t=Qa,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=_.T,_.T=null;var a=X.p;X.p=2;var i=Ee;Ee|=4;try{Jd(t,e);var r=tu,d=Fo(e.containerInfo),p=r.focusedElem,j=r.selectionRange;if(d!==p&&p&&p.ownerDocument&&Ko(p.ownerDocument.documentElement,p)){if(j!==null&&Rr(p)){var D=j.start,q=j.end;if(q===void 0&&(q=D),"selectionStart"in p)p.selectionStart=D,p.selectionEnd=Math.min(q,p.value.length);else{var V=p.ownerDocument||document,U=V&&V.defaultView||window;if(U.getSelection){var L=U.getSelection(),ce=p.textContent.length,ie=Math.min(j.start,ce),Re=j.end===void 0?ie:Math.min(j.end,ce);!L.extend&&ie>Re&&(d=Re,Re=ie,ie=d);var C=Zo(p,ie),A=Zo(p,Re);if(C&&A&&(L.rangeCount!==1||L.anchorNode!==C.node||L.anchorOffset!==C.offset||L.focusNode!==A.node||L.focusOffset!==A.offset)){var O=V.createRange();O.setStart(C.node,C.offset),L.removeAllRanges(),ie>Re?(L.addRange(O),L.extend(A.node,A.offset)):(O.setEnd(A.node,A.offset),L.addRange(O))}}}}for(V=[],L=p;L=L.parentNode;)L.nodeType===1&&V.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var Y=V[p];Y.element.scrollLeft=Y.left,Y.element.scrollTop=Y.top}}ws=!!eu,tu=eu=null}finally{Ee=i,X.p=a,_.T=l}}e.current=t,We=2}}function um(){if(We===2){We=0;var e=Dl,t=Qa,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=_.T,_.T=null;var a=X.p;X.p=2;var i=Ee;Ee|=4;try{Yd(e,t.alternate,t)}finally{Ee=i,X.p=a,_.T=l}}We=3}}function om(){if(We===4||We===3){We=0,Dh();var e=Dl,t=Qa,l=ka,a=Pd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?We=5:(We=0,Qa=Dl=null,fm(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Ol=null),rr(l),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(sn,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=_.T,i=X.p,X.p=2,_.T=null;try{for(var r=e.onRecoverableError,d=0;d<a.length;d++){var p=a[d];r(p.value,{componentStack:p.stack})}}finally{_.T=t,X.p=i}}(ka&3)!==0&&ms(),Kt(e),i=e.pendingLanes,(l&4194090)!==0&&(i&42)!==0?e===Yc?Jn++:(Jn=0,Yc=e):Jn=0,Qn(0)}}function fm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,En(t)))}function ms(e){return cm(),um(),om(),dm()}function dm(){if(We!==5)return!1;var e=Dl,t=Bc;Bc=0;var l=rr(ka),a=_.T,i=X.p;try{X.p=32>l?32:l,_.T=null,l=qc,qc=null;var r=Dl,d=ka;if(We=0,Qa=Dl=null,ka=0,(Ee&6)!==0)throw Error(u(331));var p=Ee;if(Ee|=4,Fd(r.current),kd(r,r.current,d,l),Ee=p,Qn(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(sn,r)}catch{}return!0}finally{X.p=i,_.T=a,fm(e,t)}}function mm(e,t,l){t=Rt(l,t),t=gc(e.stateNode,t,2),e=Nl(e,t,2),e!==null&&(cn(e,2),Kt(e))}function Ce(e,t,l){if(e.tag===3)mm(e,e,l);else for(;t!==null;){if(t.tag===3){mm(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Ol===null||!Ol.has(a))){e=Rt(l,e),l=yd(2),a=Nl(t,l,2),a!==null&&(gd(l,a,t,e),cn(a,2),Kt(a));break}}t=t.return}}function Jc(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new i2;var i=new Set;a.set(t,i)}else i=a.get(t),i===void 0&&(i=new Set,a.set(t,i));i.has(l)||(_c=!0,i.add(l),e=o2.bind(null,e,t,l),t.then(e,e))}function o2(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Oe===e&&(be&l)===l&&(qe===4||qe===3&&(be&62914560)===be&&300>Xt()-Hc?(Ee&2)===0&&Za(e,0):Uc|=l,Ja===be&&(Ja=0)),Kt(e)}function hm(e,t){t===0&&(t=co()),e=Ca(e,t),e!==null&&(cn(e,t),Kt(e))}function f2(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),hm(e,l)}function d2(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,i=e.memoizedState;i!==null&&(l=i.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(u(314))}a!==null&&a.delete(t),hm(e,l)}function m2(e,t){return ar(e,t)}var hs=null,Fa=null,Qc=!1,ps=!1,kc=!1,ra=0;function Kt(e){e!==Fa&&e.next===null&&(Fa===null?hs=Fa=e:Fa=Fa.next=e),ps=!0,Qc||(Qc=!0,p2())}function Qn(e,t){if(!kc&&ps){kc=!0;do for(var l=!1,a=hs;a!==null;){if(e!==0){var i=a.pendingLanes;if(i===0)var r=0;else{var d=a.suspendedLanes,p=a.pingedLanes;r=(1<<31-pt(42|e)+1)-1,r&=i&~(d&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(l=!0,gm(a,r))}else r=be,r=ji(a,a===Oe?r:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(r&3)===0||rn(a,r)||(l=!0,gm(a,r));a=a.next}while(l);kc=!1}}function h2(){pm()}function pm(){ps=Qc=!1;var e=0;ra!==0&&(S2()&&(e=ra),ra=0);for(var t=Xt(),l=null,a=hs;a!==null;){var i=a.next,r=xm(a,t);r===0?(a.next=null,l===null?hs=i:l.next=i,i===null&&(Fa=l)):(l=a,(e!==0||(r&3)!==0)&&(ps=!0)),a=i}Qn(e)}function xm(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,i=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var d=31-pt(r),p=1<<d,j=i[d];j===-1?((p&l)===0||(p&a)!==0)&&(i[d]=Yh(p,t)):j<=t&&(e.expiredLanes|=p),r&=~p}if(t=Oe,l=be,l=ji(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(we===2||we===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&nr(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||rn(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&nr(a),rr(l)){case 2:case 8:l=io;break;case 32:l=gi;break;case 268435456:l=so;break;default:l=gi}return a=ym.bind(null,e),l=ar(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&nr(a),e.callbackPriority=2,e.callbackNode=null,2}function ym(e,t){if(We!==0&&We!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(ms()&&e.callbackNode!==l)return null;var a=be;return a=ji(e,e===Oe?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Id(e,a,t),xm(e,Xt()),e.callbackNode!=null&&e.callbackNode===l?ym.bind(null,e):null)}function gm(e,t){if(ms())return null;Id(e,t,!0)}function p2(){w2(function(){(Ee&6)!==0?ar(no,h2):pm()})}function Zc(){return ra===0&&(ra=ro()),ra}function vm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ai(""+e)}function bm(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function x2(e,t,l,a,i){if(t==="submit"&&l&&l.stateNode===i){var r=vm((i[st]||null).action),d=a.submitter;d&&(t=(t=d[st]||null)?vm(t.formAction):d.getAttribute("formAction"),t!==null&&(r=t,d=null));var p=new zi("action","action",null,a,i);e.push({event:p,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ra!==0){var j=d?bm(i,d):new FormData(i);mc(l,{pending:!0,data:j,method:i.method,action:r},null,j)}}else typeof r=="function"&&(p.preventDefault(),j=d?bm(i,d):new FormData(i),mc(l,{pending:!0,data:j,method:i.method,action:r},r,j))},currentTarget:i}]})}}for(var Kc=0;Kc<Dr.length;Kc++){var Fc=Dr[Kc],y2=Fc.toLowerCase(),g2=Fc[0].toUpperCase()+Fc.slice(1);Lt(y2,"on"+g2)}Lt(Wo,"onAnimationEnd"),Lt(Io,"onAnimationIteration"),Lt(ef,"onAnimationStart"),Lt("dblclick","onDoubleClick"),Lt("focusin","onFocus"),Lt("focusout","onBlur"),Lt(U1,"onTransitionRun"),Lt(L1,"onTransitionStart"),Lt(H1,"onTransitionCancel"),Lt(tf,"onTransitionEnd"),va("onMouseEnter",["mouseout","mouseover"]),va("onMouseLeave",["mouseout","mouseover"]),va("onPointerEnter",["pointerout","pointerover"]),va("onPointerLeave",["pointerout","pointerover"]),Ql("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ql("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ql("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ql("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ql("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ql("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),v2=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(kn));function jm(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],i=a.event;a=a.listeners;e:{var r=void 0;if(t)for(var d=a.length-1;0<=d;d--){var p=a[d],j=p.instance,D=p.currentTarget;if(p=p.listener,j!==r&&i.isPropagationStopped())break e;r=p,i.currentTarget=D;try{r(i)}catch(q){ls(q)}i.currentTarget=null,r=j}else for(d=0;d<a.length;d++){if(p=a[d],j=p.instance,D=p.currentTarget,p=p.listener,j!==r&&i.isPropagationStopped())break e;r=p,i.currentTarget=D;try{r(i)}catch(q){ls(q)}i.currentTarget=null,r=j}}}}function ve(e,t){var l=t[cr];l===void 0&&(l=t[cr]=new Set);var a=e+"__bubble";l.has(a)||(Nm(t,e,2,!1),l.add(a))}function $c(e,t,l){var a=0;t&&(a|=4),Nm(l,e,a,t)}var xs="_reactListening"+Math.random().toString(36).slice(2);function Pc(e){if(!e[xs]){e[xs]=!0,ho.forEach(function(l){l!=="selectionchange"&&(v2.has(l)||$c(l,!1,e),$c(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xs]||(t[xs]=!0,$c("selectionchange",!1,t))}}function Nm(e,t,l,a){switch(km(t)){case 2:var i=k2;break;case 8:i=Z2;break;default:i=fu}l=i.bind(null,t,l,e),i=void 0,!vr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),a?i!==void 0?e.addEventListener(t,l,{capture:!0,passive:i}):e.addEventListener(t,l,!0):i!==void 0?e.addEventListener(t,l,{passive:i}):e.addEventListener(t,l,!1)}function Wc(e,t,l,a,i){var r=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var d=a.tag;if(d===3||d===4){var p=a.stateNode.containerInfo;if(p===i)break;if(d===4)for(d=a.return;d!==null;){var j=d.tag;if((j===3||j===4)&&d.stateNode.containerInfo===i)return;d=d.return}for(;p!==null;){if(d=xa(p),d===null)return;if(j=d.tag,j===5||j===6||j===26||j===27){a=r=d;continue e}p=p.parentNode}}a=a.return}Ro(function(){var D=r,q=yr(l),V=[];e:{var U=lf.get(e);if(U!==void 0){var L=zi,ce=e;switch(e){case"keypress":if(Ri(l)===0)break e;case"keydown":case"keyup":L=m1;break;case"focusin":ce="focus",L=Sr;break;case"focusout":ce="blur",L=Sr;break;case"beforeblur":case"afterblur":L=Sr;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=Oo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=t1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=x1;break;case Wo:case Io:case ef:L=n1;break;case tf:L=g1;break;case"scroll":case"scrollend":L=Ih;break;case"wheel":L=b1;break;case"copy":case"cut":case"paste":L=s1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=Mo;break;case"toggle":case"beforetoggle":L=N1}var ie=(t&4)!==0,Re=!ie&&(e==="scroll"||e==="scrollend"),C=ie?U!==null?U+"Capture":null:U;ie=[];for(var A=D,O;A!==null;){var Y=A;if(O=Y.stateNode,Y=Y.tag,Y!==5&&Y!==26&&Y!==27||O===null||C===null||(Y=fn(A,C),Y!=null&&ie.push(Zn(A,Y,O))),Re)break;A=A.return}0<ie.length&&(U=new L(U,ce,null,l,q),V.push({event:U,listeners:ie}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",L=e==="mouseout"||e==="pointerout",U&&l!==xr&&(ce=l.relatedTarget||l.fromElement)&&(xa(ce)||ce[pa]))break e;if((L||U)&&(U=q.window===q?q:(U=q.ownerDocument)?U.defaultView||U.parentWindow:window,L?(ce=l.relatedTarget||l.toElement,L=D,ce=ce?xa(ce):null,ce!==null&&(Re=m(ce),ie=ce.tag,ce!==Re||ie!==5&&ie!==27&&ie!==6)&&(ce=null)):(L=null,ce=D),L!==ce)){if(ie=Oo,Y="onMouseLeave",C="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(ie=Mo,Y="onPointerLeave",C="onPointerEnter",A="pointer"),Re=L==null?U:on(L),O=ce==null?U:on(ce),U=new ie(Y,A+"leave",L,l,q),U.target=Re,U.relatedTarget=O,Y=null,xa(q)===D&&(ie=new ie(C,A+"enter",ce,l,q),ie.target=O,ie.relatedTarget=Re,Y=ie),Re=Y,L&&ce)t:{for(ie=L,C=ce,A=0,O=ie;O;O=$a(O))A++;for(O=0,Y=C;Y;Y=$a(Y))O++;for(;0<A-O;)ie=$a(ie),A--;for(;0<O-A;)C=$a(C),O--;for(;A--;){if(ie===C||C!==null&&ie===C.alternate)break t;ie=$a(ie),C=$a(C)}ie=null}else ie=null;L!==null&&Sm(V,U,L,ie,!1),ce!==null&&Re!==null&&Sm(V,Re,ce,ie,!0)}}e:{if(U=D?on(D):window,L=U.nodeName&&U.nodeName.toLowerCase(),L==="select"||L==="input"&&U.type==="file")var $=Vo;else if(qo(U))if(Go)$=D1;else{$=z1;var ye=C1}else L=U.nodeName,!L||L.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?D&&pr(D.elementType)&&($=Vo):$=O1;if($&&($=$(e,D))){Yo(V,$,l,q);break e}ye&&ye(e,U,D),e==="focusout"&&D&&U.type==="number"&&D.memoizedProps.value!=null&&hr(U,"number",U.value)}switch(ye=D?on(D):window,e){case"focusin":(qo(ye)||ye.contentEditable==="true")&&(Aa=ye,Cr=D,vn=null);break;case"focusout":vn=Cr=Aa=null;break;case"mousedown":zr=!0;break;case"contextmenu":case"mouseup":case"dragend":zr=!1,$o(V,l,q);break;case"selectionchange":if(_1)break;case"keydown":case"keyup":$o(V,l,q)}var I;if(wr)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else wa?Ho(e,l)&&(se="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(se="onCompositionStart");se&&(_o&&l.locale!=="ko"&&(wa||se!=="onCompositionStart"?se==="onCompositionEnd"&&wa&&(I=Co()):(gl=q,br="value"in gl?gl.value:gl.textContent,wa=!0)),ye=ys(D,se),0<ye.length&&(se=new Do(se,e,null,l,q),V.push({event:se,listeners:ye}),I?se.data=I:(I=Bo(l),I!==null&&(se.data=I)))),(I=E1?w1(e,l):A1(e,l))&&(se=ys(D,"onBeforeInput"),0<se.length&&(ye=new Do("onBeforeInput","beforeinput",null,l,q),V.push({event:ye,listeners:se}),ye.data=I)),x2(V,e,D,l,q)}jm(V,t)})}function Zn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function ys(e,t){for(var l=t+"Capture",a=[];e!==null;){var i=e,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=fn(e,l),i!=null&&a.unshift(Zn(e,i,r)),i=fn(e,t),i!=null&&a.push(Zn(e,i,r))),e.tag===3)return a;e=e.return}return[]}function $a(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Sm(e,t,l,a,i){for(var r=t._reactName,d=[];l!==null&&l!==a;){var p=l,j=p.alternate,D=p.stateNode;if(p=p.tag,j!==null&&j===a)break;p!==5&&p!==26&&p!==27||D===null||(j=D,i?(D=fn(l,r),D!=null&&d.unshift(Zn(l,D,j))):i||(D=fn(l,r),D!=null&&d.push(Zn(l,D,j)))),l=l.return}d.length!==0&&e.push({event:t,listeners:d})}var b2=/\r\n?/g,j2=/\u0000|\uFFFD/g;function Em(e){return(typeof e=="string"?e:""+e).replace(b2,`
`).replace(j2,"")}function wm(e,t){return t=Em(t),Em(e)===t}function gs(){}function Te(e,t,l,a,i,r){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Na(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Na(e,""+a);break;case"className":Si(e,"class",a);break;case"tabIndex":Si(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Si(e,l,a);break;case"style":Ao(e,a,r);break;case"data":if(t!=="object"){Si(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Ai(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(l==="formAction"?(t!=="input"&&Te(e,t,"name",i.name,i,null),Te(e,t,"formEncType",i.formEncType,i,null),Te(e,t,"formMethod",i.formMethod,i,null),Te(e,t,"formTarget",i.formTarget,i,null)):(Te(e,t,"encType",i.encType,i,null),Te(e,t,"method",i.method,i,null),Te(e,t,"target",i.target,i,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Ai(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=gs);break;case"onScroll":a!=null&&ve("scroll",e);break;case"onScrollEnd":a!=null&&ve("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(u(61));if(l=a.__html,l!=null){if(i.children!=null)throw Error(u(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Ai(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":ve("beforetoggle",e),ve("toggle",e),Ni(e,"popover",a);break;case"xlinkActuate":$t(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":$t(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":$t(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":$t(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":$t(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":$t(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":$t(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":$t(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":$t(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Ni(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Ph.get(l)||l,Ni(e,l,a))}}function Ic(e,t,l,a,i,r){switch(l){case"style":Ao(e,a,r);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(u(61));if(l=a.__html,l!=null){if(i.children!=null)throw Error(u(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Na(e,a):(typeof a=="number"||typeof a=="bigint")&&Na(e,""+a);break;case"onScroll":a!=null&&ve("scroll",e);break;case"onScrollEnd":a!=null&&ve("scrollend",e);break;case"onClick":a!=null&&(e.onclick=gs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!po.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(i=l.endsWith("Capture"),t=l.slice(2,i?l.length-7:void 0),r=e[st]||null,r=r!=null?r[l]:null,typeof r=="function"&&e.removeEventListener(t,r,i),typeof a=="function")){typeof r!="function"&&r!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,i);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Ni(e,l,a)}}}function Ie(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ve("error",e),ve("load",e);var a=!1,i=!1,r;for(r in l)if(l.hasOwnProperty(r)){var d=l[r];if(d!=null)switch(r){case"src":a=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Te(e,t,r,d,l,null)}}i&&Te(e,t,"srcSet",l.srcSet,l,null),a&&Te(e,t,"src",l.src,l,null);return;case"input":ve("invalid",e);var p=r=d=i=null,j=null,D=null;for(a in l)if(l.hasOwnProperty(a)){var q=l[a];if(q!=null)switch(a){case"name":i=q;break;case"type":d=q;break;case"checked":j=q;break;case"defaultChecked":D=q;break;case"value":r=q;break;case"defaultValue":p=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(u(137,t));break;default:Te(e,t,a,q,l,null)}}No(e,r,p,j,D,d,i,!1),Ei(e);return;case"select":ve("invalid",e),a=d=r=null;for(i in l)if(l.hasOwnProperty(i)&&(p=l[i],p!=null))switch(i){case"value":r=p;break;case"defaultValue":d=p;break;case"multiple":a=p;default:Te(e,t,i,p,l,null)}t=r,l=d,e.multiple=!!a,t!=null?ja(e,!!a,t,!1):l!=null&&ja(e,!!a,l,!0);return;case"textarea":ve("invalid",e),r=i=a=null;for(d in l)if(l.hasOwnProperty(d)&&(p=l[d],p!=null))switch(d){case"value":a=p;break;case"defaultValue":i=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(u(91));break;default:Te(e,t,d,p,l,null)}Eo(e,a,i,r),Ei(e);return;case"option":for(j in l)if(l.hasOwnProperty(j)&&(a=l[j],a!=null))switch(j){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Te(e,t,j,a,l,null)}return;case"dialog":ve("beforetoggle",e),ve("toggle",e),ve("cancel",e),ve("close",e);break;case"iframe":case"object":ve("load",e);break;case"video":case"audio":for(a=0;a<kn.length;a++)ve(kn[a],e);break;case"image":ve("error",e),ve("load",e);break;case"details":ve("toggle",e);break;case"embed":case"source":case"link":ve("error",e),ve("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in l)if(l.hasOwnProperty(D)&&(a=l[D],a!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Te(e,t,D,a,l,null)}return;default:if(pr(t)){for(q in l)l.hasOwnProperty(q)&&(a=l[q],a!==void 0&&Ic(e,t,q,a,l,void 0));return}}for(p in l)l.hasOwnProperty(p)&&(a=l[p],a!=null&&Te(e,t,p,a,l,null))}function N2(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,d=null,p=null,j=null,D=null,q=null;for(L in l){var V=l[L];if(l.hasOwnProperty(L)&&V!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":j=V;default:a.hasOwnProperty(L)||Te(e,t,L,null,a,V)}}for(var U in a){var L=a[U];if(V=l[U],a.hasOwnProperty(U)&&(L!=null||V!=null))switch(U){case"type":r=L;break;case"name":i=L;break;case"checked":D=L;break;case"defaultChecked":q=L;break;case"value":d=L;break;case"defaultValue":p=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(u(137,t));break;default:L!==V&&Te(e,t,U,L,a,V)}}mr(e,d,p,j,D,q,r,i);return;case"select":L=d=p=U=null;for(r in l)if(j=l[r],l.hasOwnProperty(r)&&j!=null)switch(r){case"value":break;case"multiple":L=j;default:a.hasOwnProperty(r)||Te(e,t,r,null,a,j)}for(i in a)if(r=a[i],j=l[i],a.hasOwnProperty(i)&&(r!=null||j!=null))switch(i){case"value":U=r;break;case"defaultValue":p=r;break;case"multiple":d=r;default:r!==j&&Te(e,t,i,r,a,j)}t=p,l=d,a=L,U!=null?ja(e,!!l,U,!1):!!a!=!!l&&(t!=null?ja(e,!!l,t,!0):ja(e,!!l,l?[]:"",!1));return;case"textarea":L=U=null;for(p in l)if(i=l[p],l.hasOwnProperty(p)&&i!=null&&!a.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Te(e,t,p,null,a,i)}for(d in a)if(i=a[d],r=l[d],a.hasOwnProperty(d)&&(i!=null||r!=null))switch(d){case"value":U=i;break;case"defaultValue":L=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(u(91));break;default:i!==r&&Te(e,t,d,i,a,r)}So(e,U,L);return;case"option":for(var ce in l)if(U=l[ce],l.hasOwnProperty(ce)&&U!=null&&!a.hasOwnProperty(ce))switch(ce){case"selected":e.selected=!1;break;default:Te(e,t,ce,null,a,U)}for(j in a)if(U=a[j],L=l[j],a.hasOwnProperty(j)&&U!==L&&(U!=null||L!=null))switch(j){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Te(e,t,j,U,a,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ie in l)U=l[ie],l.hasOwnProperty(ie)&&U!=null&&!a.hasOwnProperty(ie)&&Te(e,t,ie,null,a,U);for(D in a)if(U=a[D],L=l[D],a.hasOwnProperty(D)&&U!==L&&(U!=null||L!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(u(137,t));break;default:Te(e,t,D,U,a,L)}return;default:if(pr(t)){for(var Re in l)U=l[Re],l.hasOwnProperty(Re)&&U!==void 0&&!a.hasOwnProperty(Re)&&Ic(e,t,Re,void 0,a,U);for(q in a)U=a[q],L=l[q],!a.hasOwnProperty(q)||U===L||U===void 0&&L===void 0||Ic(e,t,q,U,a,L);return}}for(var C in l)U=l[C],l.hasOwnProperty(C)&&U!=null&&!a.hasOwnProperty(C)&&Te(e,t,C,null,a,U);for(V in a)U=a[V],L=l[V],!a.hasOwnProperty(V)||U===L||U==null&&L==null||Te(e,t,V,U,a,L)}var eu=null,tu=null;function vs(e){return e.nodeType===9?e:e.ownerDocument}function Am(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Tm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function lu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var au=null;function S2(){var e=window.event;return e&&e.type==="popstate"?e===au?!1:(au=e,!0):(au=null,!1)}var Rm=typeof setTimeout=="function"?setTimeout:void 0,E2=typeof clearTimeout=="function"?clearTimeout:void 0,Cm=typeof Promise=="function"?Promise:void 0,w2=typeof queueMicrotask=="function"?queueMicrotask:typeof Cm<"u"?function(e){return Cm.resolve(null).then(e).catch(A2)}:Rm;function A2(e){setTimeout(function(){throw e})}function _l(e){return e==="head"}function zm(e,t){var l=t,a=0,i=0;do{var r=l.nextSibling;if(e.removeChild(l),r&&r.nodeType===8)if(l=r.data,l==="/$"){if(0<a&&8>a){l=a;var d=e.ownerDocument;if(l&1&&Kn(d.documentElement),l&2&&Kn(d.body),l&4)for(l=d.head,Kn(l),d=l.firstChild;d;){var p=d.nextSibling,j=d.nodeName;d[un]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&d.rel.toLowerCase()==="stylesheet"||l.removeChild(d),d=p}}if(i===0){e.removeChild(r),li(t);return}i--}else l==="$"||l==="$?"||l==="$!"?i++:a=l.charCodeAt(0)-48;else a=0;l=r}while(l);li(t)}function nu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":nu(l),ur(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function T2(e,t,l,a){for(;e.nodeType===1;){var i=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[un])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Bt(e.nextSibling),e===null)break}return null}function R2(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Bt(e.nextSibling),e===null))return null;return e}function iu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function C2(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var su=null;function Om(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Dm(e,t,l){switch(t=vs(l),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function Kn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ur(e)}var _t=new Map,Mm=new Set;function bs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ol=X.d;X.d={f:z2,r:O2,D:D2,C:M2,L:_2,m:U2,X:H2,S:L2,M:B2};function z2(){var e=ol.f(),t=fs();return e||t}function O2(e){var t=ya(e);t!==null&&t.tag===5&&t.type==="form"?If(t):ol.r(e)}var Pa=typeof document>"u"?null:document;function _m(e,t,l){var a=Pa;if(a&&typeof t=="string"&&t){var i=Tt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof l=="string"&&(i+='[crossorigin="'+l+'"]'),Mm.has(i)||(Mm.add(i),e={rel:e,crossOrigin:l,href:t},a.querySelector(i)===null&&(t=a.createElement("link"),Ie(t,"link",e),Ze(t),a.head.appendChild(t)))}}function D2(e){ol.D(e),_m("dns-prefetch",e,null)}function M2(e,t){ol.C(e,t),_m("preconnect",e,t)}function _2(e,t,l){ol.L(e,t,l);var a=Pa;if(a&&e&&t){var i='link[rel="preload"][as="'+Tt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(i+='[imagesrcset="'+Tt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(i+='[imagesizes="'+Tt(l.imageSizes)+'"]')):i+='[href="'+Tt(e)+'"]';var r=i;switch(t){case"style":r=Wa(e);break;case"script":r=Ia(e)}_t.has(r)||(e=v({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),_t.set(r,e),a.querySelector(i)!==null||t==="style"&&a.querySelector(Fn(r))||t==="script"&&a.querySelector($n(r))||(t=a.createElement("link"),Ie(t,"link",e),Ze(t),a.head.appendChild(t)))}}function U2(e,t){ol.m(e,t);var l=Pa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Tt(a)+'"][href="'+Tt(e)+'"]',r=i;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Ia(e)}if(!_t.has(r)&&(e=v({rel:"modulepreload",href:e},t),_t.set(r,e),l.querySelector(i)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector($n(r)))return}a=l.createElement("link"),Ie(a,"link",e),Ze(a),l.head.appendChild(a)}}}function L2(e,t,l){ol.S(e,t,l);var a=Pa;if(a&&e){var i=ga(a).hoistableStyles,r=Wa(e);t=t||"default";var d=i.get(r);if(!d){var p={loading:0,preload:null};if(d=a.querySelector(Fn(r)))p.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},l),(l=_t.get(r))&&ru(e,l);var j=d=a.createElement("link");Ze(j),Ie(j,"link",e),j._p=new Promise(function(D,q){j.onload=D,j.onerror=q}),j.addEventListener("load",function(){p.loading|=1}),j.addEventListener("error",function(){p.loading|=2}),p.loading|=4,js(d,t,a)}d={type:"stylesheet",instance:d,count:1,state:p},i.set(r,d)}}}function H2(e,t){ol.X(e,t);var l=Pa;if(l&&e){var a=ga(l).hoistableScripts,i=Ia(e),r=a.get(i);r||(r=l.querySelector($n(i)),r||(e=v({src:e,async:!0},t),(t=_t.get(i))&&cu(e,t),r=l.createElement("script"),Ze(r),Ie(r,"link",e),l.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(i,r))}}function B2(e,t){ol.M(e,t);var l=Pa;if(l&&e){var a=ga(l).hoistableScripts,i=Ia(e),r=a.get(i);r||(r=l.querySelector($n(i)),r||(e=v({src:e,async:!0,type:"module"},t),(t=_t.get(i))&&cu(e,t),r=l.createElement("script"),Ze(r),Ie(r,"link",e),l.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(i,r))}}function Um(e,t,l,a){var i=(i=fe.current)?bs(i):null;if(!i)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Wa(l.href),l=ga(i).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Wa(l.href);var r=ga(i).hoistableStyles,d=r.get(e);if(d||(i=i.ownerDocument||i,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,d),(r=i.querySelector(Fn(e)))&&!r._p&&(d.instance=r,d.state.loading=5),_t.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},_t.set(e,l),r||q2(i,e,l,d.state))),t&&a===null)throw Error(u(528,""));return d}if(t&&a!==null)throw Error(u(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ia(l),l=ga(i).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Wa(e){return'href="'+Tt(e)+'"'}function Fn(e){return'link[rel="stylesheet"]['+e+"]"}function Lm(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function q2(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ie(t,"link",l),Ze(t),e.head.appendChild(t))}function Ia(e){return'[src="'+Tt(e)+'"]'}function $n(e){return"script[async]"+e}function Hm(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Tt(l.href)+'"]');if(a)return t.instance=a,Ze(a),a;var i=v({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ze(a),Ie(a,"style",i),js(a,l.precedence,e),t.instance=a;case"stylesheet":i=Wa(l.href);var r=e.querySelector(Fn(i));if(r)return t.state.loading|=4,t.instance=r,Ze(r),r;a=Lm(l),(i=_t.get(i))&&ru(a,i),r=(e.ownerDocument||e).createElement("link"),Ze(r);var d=r;return d._p=new Promise(function(p,j){d.onload=p,d.onerror=j}),Ie(r,"link",a),t.state.loading|=4,js(r,l.precedence,e),t.instance=r;case"script":return r=Ia(l.src),(i=e.querySelector($n(r)))?(t.instance=i,Ze(i),i):(a=l,(i=_t.get(r))&&(a=v({},l),cu(a,i)),e=e.ownerDocument||e,i=e.createElement("script"),Ze(i),Ie(i,"link",a),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,js(a,l.precedence,e));return t.instance}function js(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=a.length?a[a.length-1]:null,r=i,d=0;d<a.length;d++){var p=a[d];if(p.dataset.precedence===t)r=p;else if(r!==i)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function ru(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function cu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ns=null;function Bm(e,t,l){if(Ns===null){var a=new Map,i=Ns=new Map;i.set(l,a)}else i=Ns,a=i.get(l),a||(a=new Map,i.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),i=0;i<l.length;i++){var r=l[i];if(!(r[un]||r[et]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var d=r.getAttribute(t)||"";d=e+d;var p=a.get(d);p?p.push(r):a.set(d,[r])}}return a}function qm(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Y2(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Ym(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Pn=null;function V2(){}function G2(e,t,l){if(Pn===null)throw Error(u(475));var a=Pn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=Wa(l.href),r=e.querySelector(Fn(i));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Ss.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=r,Ze(r);return}r=e.ownerDocument||e,l=Lm(l),(i=_t.get(i))&&ru(l,i),r=r.createElement("link"),Ze(r);var d=r;d._p=new Promise(function(p,j){d.onload=p,d.onerror=j}),Ie(r,"link",l),t.instance=r}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Ss.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function X2(){if(Pn===null)throw Error(u(475));var e=Pn;return e.stylesheets&&e.count===0&&uu(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&uu(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Ss(){if(this.count--,this.count===0){if(this.stylesheets)uu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Es=null;function uu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Es=new Map,t.forEach(J2,e),Es=null,Ss.call(e))}function J2(e,t){if(!(t.state.loading&4)){var l=Es.get(e);if(l)var a=l.get(null);else{l=new Map,Es.set(e,l);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var d=i[r];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(l.set(d.dataset.precedence,d),a=d)}a&&l.set(null,a)}i=t.instance,d=i.getAttribute("data-precedence"),r=l.get(d)||a,r===a&&l.set(null,i),l.set(d,i),this.count++,a=Ss.bind(this),i.addEventListener("load",a),i.addEventListener("error",a),r?r.parentNode.insertBefore(i,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Wn={$$typeof:J,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function Q2(e,t,l,a,i,r,d,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ir(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ir(0),this.hiddenUpdates=ir(null),this.identifierPrefix=a,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function Vm(e,t,l,a,i,r,d,p,j,D,q,V){return e=new Q2(e,t,l,d,p,j,D,V),t=1,r===!0&&(t|=24),r=yt(3,null,null,t),e.current=r,r.stateNode=e,t=Jr(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:a,isDehydrated:l,cache:t},Kr(r),e}function Gm(e){return e?(e=za,e):za}function Xm(e,t,l,a,i,r){i=Gm(i),a.context===null?a.context=i:a.pendingContext=i,a=jl(t),a.payload={element:l},r=r===void 0?null:r,r!==null&&(a.callback=r),l=Nl(e,a,t),l!==null&&(Nt(l,e,t),Rn(l,e,t))}function Jm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function ou(e,t){Jm(e,t),(e=e.alternate)&&Jm(e,t)}function Qm(e){if(e.tag===13){var t=Ca(e,67108864);t!==null&&Nt(t,e,67108864),ou(e,67108864)}}var ws=!0;function k2(e,t,l,a){var i=_.T;_.T=null;var r=X.p;try{X.p=2,fu(e,t,l,a)}finally{X.p=r,_.T=i}}function Z2(e,t,l,a){var i=_.T;_.T=null;var r=X.p;try{X.p=8,fu(e,t,l,a)}finally{X.p=r,_.T=i}}function fu(e,t,l,a){if(ws){var i=du(a);if(i===null)Wc(e,t,a,As,l),Zm(e,a);else if(F2(i,e,t,l,a))a.stopPropagation();else if(Zm(e,a),t&4&&-1<K2.indexOf(e)){for(;i!==null;){var r=ya(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var d=Jl(r.pendingLanes);if(d!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;d;){var j=1<<31-pt(d);p.entanglements[1]|=j,d&=~j}Kt(r),(Ee&6)===0&&(us=Xt()+500,Qn(0))}}break;case 13:p=Ca(r,2),p!==null&&Nt(p,r,2),fs(),ou(r,2)}if(r=du(a),r===null&&Wc(e,t,a,As,l),r===i)break;i=r}i!==null&&a.stopPropagation()}else Wc(e,t,a,null,l)}}function du(e){return e=yr(e),mu(e)}var As=null;function mu(e){if(As=null,e=xa(e),e!==null){var t=m(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=h(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return As=e,null}function km(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Mh()){case no:return 2;case io:return 8;case gi:case _h:return 32;case so:return 268435456;default:return 32}default:return 32}}var hu=!1,Ul=null,Ll=null,Hl=null,In=new Map,ei=new Map,Bl=[],K2="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Zm(e,t){switch(e){case"focusin":case"focusout":Ul=null;break;case"dragenter":case"dragleave":Ll=null;break;case"mouseover":case"mouseout":Hl=null;break;case"pointerover":case"pointerout":In.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ei.delete(t.pointerId)}}function ti(e,t,l,a,i,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:r,targetContainers:[i]},t!==null&&(t=ya(t),t!==null&&Qm(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function F2(e,t,l,a,i){switch(t){case"focusin":return Ul=ti(Ul,e,t,l,a,i),!0;case"dragenter":return Ll=ti(Ll,e,t,l,a,i),!0;case"mouseover":return Hl=ti(Hl,e,t,l,a,i),!0;case"pointerover":var r=i.pointerId;return In.set(r,ti(In.get(r)||null,e,t,l,a,i)),!0;case"gotpointercapture":return r=i.pointerId,ei.set(r,ti(ei.get(r)||null,e,t,l,a,i)),!0}return!1}function Km(e){var t=xa(e.target);if(t!==null){var l=m(t);if(l!==null){if(t=l.tag,t===13){if(t=h(l),t!==null){e.blockedOn=t,Gh(e.priority,function(){if(l.tag===13){var a=jt();a=sr(a);var i=Ca(l,a);i!==null&&Nt(i,l,a),ou(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ts(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=du(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);xr=a,l.target.dispatchEvent(a),xr=null}else return t=ya(l),t!==null&&Qm(t),e.blockedOn=l,!1;t.shift()}return!0}function Fm(e,t,l){Ts(e)&&l.delete(t)}function $2(){hu=!1,Ul!==null&&Ts(Ul)&&(Ul=null),Ll!==null&&Ts(Ll)&&(Ll=null),Hl!==null&&Ts(Hl)&&(Hl=null),In.forEach(Fm),ei.forEach(Fm)}function Rs(e,t){e.blockedOn===t&&(e.blockedOn=null,hu||(hu=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,$2)))}var Cs=null;function $m(e){Cs!==e&&(Cs=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Cs===e&&(Cs=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],i=e[t+2];if(typeof a!="function"){if(mu(a||l)===null)continue;break}var r=ya(l);r!==null&&(e.splice(t,3),t-=3,mc(r,{pending:!0,data:i,method:l.method,action:a},a,i))}}))}function li(e){function t(j){return Rs(j,e)}Ul!==null&&Rs(Ul,e),Ll!==null&&Rs(Ll,e),Hl!==null&&Rs(Hl,e),In.forEach(t),ei.forEach(t);for(var l=0;l<Bl.length;l++){var a=Bl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Bl.length&&(l=Bl[0],l.blockedOn===null);)Km(l),l.blockedOn===null&&Bl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var i=l[a],r=l[a+1],d=i[st]||null;if(typeof r=="function")d||$m(l);else if(d){var p=null;if(r&&r.hasAttribute("formAction")){if(i=r,d=r[st]||null)p=d.formAction;else if(mu(i)!==null)continue}else p=d.action;typeof p=="function"?l[a+1]=p:(l.splice(a,3),a-=3),$m(l)}}}function pu(e){this._internalRoot=e}zs.prototype.render=pu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var l=t.current,a=jt();Xm(l,a,e,t,null,null)},zs.prototype.unmount=pu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Xm(e.current,2,null,e,null,null),fs(),t[pa]=null}};function zs(e){this._internalRoot=e}zs.prototype.unstable_scheduleHydration=function(e){if(e){var t=fo();e={blockedOn:null,target:e,priority:t};for(var l=0;l<Bl.length&&t!==0&&t<Bl[l].priority;l++);Bl.splice(l,0,e),l===0&&Km(e)}};var Pm=c.version;if(Pm!=="19.1.0")throw Error(u(527,Pm,"19.1.0"));X.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=y(t),e=e!==null?x(e):null,e=e===null?null:e.stateNode,e};var P2={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Os=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Os.isDisabled&&Os.supportsFiber)try{sn=Os.inject(P2),ht=Os}catch{}}return ni.createRoot=function(e,t){if(!f(e))throw Error(u(299));var l=!1,a="",i=md,r=hd,d=pd,p=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=Vm(e,1,!1,null,null,l,a,i,r,d,p,null),e[pa]=t.current,Pc(e),new pu(t)},ni.hydrateRoot=function(e,t,l){if(!f(e))throw Error(u(299));var a=!1,i="",r=md,d=hd,p=pd,j=null,D=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(i=l.identifierPrefix),l.onUncaughtError!==void 0&&(r=l.onUncaughtError),l.onCaughtError!==void 0&&(d=l.onCaughtError),l.onRecoverableError!==void 0&&(p=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(j=l.unstable_transitionCallbacks),l.formState!==void 0&&(D=l.formState)),t=Vm(e,1,!0,t,l??null,a,i,r,d,p,j,D),t.context=Gm(null),l=t.current,a=jt(),a=sr(a),i=jl(a),i.callback=null,Nl(l,i,a),l=a,t.current.lanes=l,cn(t,l),Kt(t),e[pa]=t.current,Pc(e),new zs(t)},ni.version="19.1.0",ni}var r0;function rp(){if(r0)return gu.exports;r0=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(c){console.error(c)}}return n(),gu.exports=sp(),gu.exports}var cp=rp();const up=O0(cp);var ii={},c0;function op(){if(c0)return ii;c0=1,Object.defineProperty(ii,"__esModule",{value:!0}),ii.parse=h,ii.serialize=x;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,c=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,u=/^[\u0020-\u003A\u003D-\u007E]*$/,f=Object.prototype.toString,m=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function h(S,H){const b=new m,E=S.length;if(E<2)return b;const T=(H==null?void 0:H.decode)||v;let z=0;do{const M=S.indexOf("=",z);if(M===-1)break;const J=S.indexOf(";",z),k=J===-1?E:J;if(M>k){z=S.lastIndexOf(";",M-1)+1;continue}const Q=g(S,z,M),Z=y(S,M,Q),le=S.slice(Q,Z);if(b[le]===void 0){let xe=g(S,M+1,k),oe=y(S,k,xe);const ze=T(S.slice(xe,oe));b[le]=ze}z=k+1}while(z<E);return b}function g(S,H,b){do{const E=S.charCodeAt(H);if(E!==32&&E!==9)return H}while(++H<b);return b}function y(S,H,b){for(;H>b;){const E=S.charCodeAt(--H);if(E!==32&&E!==9)return H+1}return b}function x(S,H,b){const E=(b==null?void 0:b.encode)||encodeURIComponent;if(!n.test(S))throw new TypeError(`argument name is invalid: ${S}`);const T=E(H);if(!c.test(T))throw new TypeError(`argument val is invalid: ${H}`);let z=S+"="+T;if(!b)return z;if(b.maxAge!==void 0){if(!Number.isInteger(b.maxAge))throw new TypeError(`option maxAge is invalid: ${b.maxAge}`);z+="; Max-Age="+b.maxAge}if(b.domain){if(!o.test(b.domain))throw new TypeError(`option domain is invalid: ${b.domain}`);z+="; Domain="+b.domain}if(b.path){if(!u.test(b.path))throw new TypeError(`option path is invalid: ${b.path}`);z+="; Path="+b.path}if(b.expires){if(!N(b.expires)||!Number.isFinite(b.expires.valueOf()))throw new TypeError(`option expires is invalid: ${b.expires}`);z+="; Expires="+b.expires.toUTCString()}if(b.httpOnly&&(z+="; HttpOnly"),b.secure&&(z+="; Secure"),b.partitioned&&(z+="; Partitioned"),b.priority)switch(typeof b.priority=="string"?b.priority.toLowerCase():void 0){case"low":z+="; Priority=Low";break;case"medium":z+="; Priority=Medium";break;case"high":z+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${b.priority}`)}if(b.sameSite)switch(typeof b.sameSite=="string"?b.sameSite.toLowerCase():b.sameSite){case!0:case"strict":z+="; SameSite=Strict";break;case"lax":z+="; SameSite=Lax";break;case"none":z+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${b.sameSite}`)}return z}function v(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function N(S){return f.call(S)==="[object Date]"}return ii}op();var u0="popstate";function fp(n={}){function c(u,f){let{pathname:m,search:h,hash:g}=u.location;return Cu("",{pathname:m,search:h,hash:g},f.state&&f.state.usr||null,f.state&&f.state.key||"default")}function o(u,f){return typeof f=="string"?f:ci(f)}return mp(c,o,null,n)}function Le(n,c){if(n===!1||n===null||typeof n>"u")throw new Error(c)}function qt(n,c){if(!n){typeof console<"u"&&console.warn(c);try{throw new Error(c)}catch{}}}function dp(){return Math.random().toString(36).substring(2,10)}function o0(n,c){return{usr:n.state,key:n.key,idx:c}}function Cu(n,c,o=null,u){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof c=="string"?en(c):c,state:o,key:c&&c.key||u||dp()}}function ci({pathname:n="/",search:c="",hash:o=""}){return c&&c!=="?"&&(n+=c.charAt(0)==="?"?c:"?"+c),o&&o!=="#"&&(n+=o.charAt(0)==="#"?o:"#"+o),n}function en(n){let c={};if(n){let o=n.indexOf("#");o>=0&&(c.hash=n.substring(o),n=n.substring(0,o));let u=n.indexOf("?");u>=0&&(c.search=n.substring(u),n=n.substring(0,u)),n&&(c.pathname=n)}return c}function mp(n,c,o,u={}){let{window:f=document.defaultView,v5Compat:m=!1}=u,h=f.history,g="POP",y=null,x=v();x==null&&(x=0,h.replaceState({...h.state,idx:x},""));function v(){return(h.state||{idx:null}).idx}function N(){g="POP";let T=v(),z=T==null?null:T-x;x=T,y&&y({action:g,location:E.location,delta:z})}function S(T,z){g="PUSH";let M=Cu(E.location,T,z);x=v()+1;let J=o0(M,x),k=E.createHref(M);try{h.pushState(J,"",k)}catch(Q){if(Q instanceof DOMException&&Q.name==="DataCloneError")throw Q;f.location.assign(k)}m&&y&&y({action:g,location:E.location,delta:1})}function H(T,z){g="REPLACE";let M=Cu(E.location,T,z);x=v();let J=o0(M,x),k=E.createHref(M);h.replaceState(J,"",k),m&&y&&y({action:g,location:E.location,delta:0})}function b(T){return hp(T)}let E={get action(){return g},get location(){return n(f,h)},listen(T){if(y)throw new Error("A history only accepts one active listener");return f.addEventListener(u0,N),y=T,()=>{f.removeEventListener(u0,N),y=null}},createHref(T){return c(f,T)},createURL:b,encodeLocation(T){let z=b(T);return{pathname:z.pathname,search:z.search,hash:z.hash}},push:S,replace:H,go(T){return h.go(T)}};return E}function hp(n,c=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),Le(o,"No window.location.(origin|href) available to create URL");let u=typeof n=="string"?n:ci(n);return u=u.replace(/ $/,"%20"),!c&&u.startsWith("//")&&(u=o+u),new URL(u,o)}function D0(n,c,o="/"){return pp(n,c,o,!1)}function pp(n,c,o,u){let f=typeof c=="string"?en(c):c,m=hl(f.pathname||"/",o);if(m==null)return null;let h=M0(n);xp(h);let g=null;for(let y=0;g==null&&y<h.length;++y){let x=Tp(m);g=wp(h[y],x,u)}return g}function M0(n,c=[],o=[],u=""){let f=(m,h,g)=>{let y={relativePath:g===void 0?m.path||"":g,caseSensitive:m.caseSensitive===!0,childrenIndex:h,route:m};y.relativePath.startsWith("/")&&(Le(y.relativePath.startsWith(u),`Absolute route path "${y.relativePath}" nested under path "${u}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),y.relativePath=y.relativePath.slice(u.length));let x=dl([u,y.relativePath]),v=o.concat(y);m.children&&m.children.length>0&&(Le(m.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${x}".`),M0(m.children,c,v,x)),!(m.path==null&&!m.index)&&c.push({path:x,score:Sp(x,m.index),routesMeta:v})};return n.forEach((m,h)=>{var g;if(m.path===""||!((g=m.path)!=null&&g.includes("?")))f(m,h);else for(let y of _0(m.path))f(m,h,y)}),c}function _0(n){let c=n.split("/");if(c.length===0)return[];let[o,...u]=c,f=o.endsWith("?"),m=o.replace(/\?$/,"");if(u.length===0)return f?[m,""]:[m];let h=_0(u.join("/")),g=[];return g.push(...h.map(y=>y===""?m:[m,y].join("/"))),f&&g.push(...h),g.map(y=>n.startsWith("/")&&y===""?"/":y)}function xp(n){n.sort((c,o)=>c.score!==o.score?o.score-c.score:Ep(c.routesMeta.map(u=>u.childrenIndex),o.routesMeta.map(u=>u.childrenIndex)))}var yp=/^:[\w-]+$/,gp=3,vp=2,bp=1,jp=10,Np=-2,f0=n=>n==="*";function Sp(n,c){let o=n.split("/"),u=o.length;return o.some(f0)&&(u+=Np),c&&(u+=vp),o.filter(f=>!f0(f)).reduce((f,m)=>f+(yp.test(m)?gp:m===""?bp:jp),u)}function Ep(n,c){return n.length===c.length&&n.slice(0,-1).every((u,f)=>u===c[f])?n[n.length-1]-c[c.length-1]:0}function wp(n,c,o=!1){let{routesMeta:u}=n,f={},m="/",h=[];for(let g=0;g<u.length;++g){let y=u[g],x=g===u.length-1,v=m==="/"?c:c.slice(m.length)||"/",N=Bs({path:y.relativePath,caseSensitive:y.caseSensitive,end:x},v),S=y.route;if(!N&&x&&o&&!u[u.length-1].route.index&&(N=Bs({path:y.relativePath,caseSensitive:y.caseSensitive,end:!1},v)),!N)return null;Object.assign(f,N.params),h.push({params:f,pathname:dl([m,N.pathname]),pathnameBase:Op(dl([m,N.pathnameBase])),route:S}),N.pathnameBase!=="/"&&(m=dl([m,N.pathnameBase]))}return h}function Bs(n,c){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[o,u]=Ap(n.path,n.caseSensitive,n.end),f=c.match(o);if(!f)return null;let m=f[0],h=m.replace(/(.)\/+$/,"$1"),g=f.slice(1);return{params:u.reduce((x,{paramName:v,isOptional:N},S)=>{if(v==="*"){let b=g[S]||"";h=m.slice(0,m.length-b.length).replace(/(.)\/+$/,"$1")}const H=g[S];return N&&!H?x[v]=void 0:x[v]=(H||"").replace(/%2F/g,"/"),x},{}),pathname:m,pathnameBase:h,pattern:n}}function Ap(n,c=!1,o=!0){qt(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let u=[],f="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,g,y)=>(u.push({paramName:g,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(u.push({paramName:"*"}),f+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?f+="\\/*$":n!==""&&n!=="/"&&(f+="(?:(?=\\/|$))"),[new RegExp(f,c?void 0:"i"),u]}function Tp(n){try{return n.split("/").map(c=>decodeURIComponent(c).replace(/\//g,"%2F")).join("/")}catch(c){return qt(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${c}).`),n}}function hl(n,c){if(c==="/")return n;if(!n.toLowerCase().startsWith(c.toLowerCase()))return null;let o=c.endsWith("/")?c.length-1:c.length,u=n.charAt(o);return u&&u!=="/"?null:n.slice(o)||"/"}function Rp(n,c="/"){let{pathname:o,search:u="",hash:f=""}=typeof n=="string"?en(n):n;return{pathname:o?o.startsWith("/")?o:Cp(o,c):c,search:Dp(u),hash:Mp(f)}}function Cp(n,c){let o=c.replace(/\/+$/,"").split("/");return n.split("/").forEach(f=>{f===".."?o.length>1&&o.pop():f!=="."&&o.push(f)}),o.length>1?o.join("/"):"/"}function Nu(n,c,o,u){return`Cannot include a '${n}' character in a manually specified \`to.${c}\` field [${JSON.stringify(u)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function zp(n){return n.filter((c,o)=>o===0||c.route.path&&c.route.path.length>0)}function Qu(n){let c=zp(n);return c.map((o,u)=>u===c.length-1?o.pathname:o.pathnameBase)}function ku(n,c,o,u=!1){let f;typeof n=="string"?f=en(n):(f={...n},Le(!f.pathname||!f.pathname.includes("?"),Nu("?","pathname","search",f)),Le(!f.pathname||!f.pathname.includes("#"),Nu("#","pathname","hash",f)),Le(!f.search||!f.search.includes("#"),Nu("#","search","hash",f)));let m=n===""||f.pathname==="",h=m?"/":f.pathname,g;if(h==null)g=o;else{let N=c.length-1;if(!u&&h.startsWith("..")){let S=h.split("/");for(;S[0]==="..";)S.shift(),N-=1;f.pathname=S.join("/")}g=N>=0?c[N]:"/"}let y=Rp(f,g),x=h&&h!=="/"&&h.endsWith("/"),v=(m||h===".")&&o.endsWith("/");return!y.pathname.endsWith("/")&&(x||v)&&(y.pathname+="/"),y}var dl=n=>n.join("/").replace(/\/\/+/g,"/"),Op=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),Dp=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,Mp=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function _p(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var U0=["POST","PUT","PATCH","DELETE"];new Set(U0);var Up=["GET",...U0];new Set(Up);var tn=R.createContext(null);tn.displayName="DataRouter";var Xs=R.createContext(null);Xs.displayName="DataRouterState";var L0=R.createContext({isTransitioning:!1});L0.displayName="ViewTransition";var Lp=R.createContext(new Map);Lp.displayName="Fetchers";var Hp=R.createContext(null);Hp.displayName="Await";var Yt=R.createContext(null);Yt.displayName="Navigation";var fi=R.createContext(null);fi.displayName="Location";var Vt=R.createContext({outlet:null,matches:[],isDataRoute:!1});Vt.displayName="Route";var Zu=R.createContext(null);Zu.displayName="RouteError";function Bp(n,{relative:c}={}){Le(ln(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:u}=R.useContext(Yt),{hash:f,pathname:m,search:h}=di(n,{relative:c}),g=m;return o!=="/"&&(g=m==="/"?o:dl([o,m])),u.createHref({pathname:g,search:h,hash:f})}function ln(){return R.useContext(fi)!=null}function Et(){return Le(ln(),"useLocation() may be used only in the context of a <Router> component."),R.useContext(fi).location}var H0="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function B0(n){R.useContext(Yt).static||R.useLayoutEffect(n)}function Xl(){let{isDataRoute:n}=R.useContext(Vt);return n?Pp():qp()}function qp(){Le(ln(),"useNavigate() may be used only in the context of a <Router> component.");let n=R.useContext(tn),{basename:c,navigator:o}=R.useContext(Yt),{matches:u}=R.useContext(Vt),{pathname:f}=Et(),m=JSON.stringify(Qu(u)),h=R.useRef(!1);return B0(()=>{h.current=!0}),R.useCallback((y,x={})=>{if(qt(h.current,H0),!h.current)return;if(typeof y=="number"){o.go(y);return}let v=ku(y,JSON.parse(m),f,x.relative==="path");n==null&&c!=="/"&&(v.pathname=v.pathname==="/"?c:dl([c,v.pathname])),(x.replace?o.replace:o.push)(v,x.state,x)},[c,o,m,f,n])}R.createContext(null);function Js(){let{matches:n}=R.useContext(Vt),c=n[n.length-1];return c?c.params:{}}function di(n,{relative:c}={}){let{matches:o}=R.useContext(Vt),{pathname:u}=Et(),f=JSON.stringify(Qu(o));return R.useMemo(()=>ku(n,JSON.parse(f),u,c==="path"),[n,f,u,c])}function Yp(n,c){return q0(n,c)}function q0(n,c,o,u){var z;Le(ln(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:f}=R.useContext(Yt),{matches:m}=R.useContext(Vt),h=m[m.length-1],g=h?h.params:{},y=h?h.pathname:"/",x=h?h.pathnameBase:"/",v=h&&h.route;{let M=v&&v.path||"";Y0(y,!v||M.endsWith("*")||M.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${y}" (under <Route path="${M}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${M}"> to <Route path="${M==="/"?"*":`${M}/*`}">.`)}let N=Et(),S;if(c){let M=typeof c=="string"?en(c):c;Le(x==="/"||((z=M.pathname)==null?void 0:z.startsWith(x)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${x}" but pathname "${M.pathname}" was given in the \`location\` prop.`),S=M}else S=N;let H=S.pathname||"/",b=H;if(x!=="/"){let M=x.replace(/^\//,"").split("/");b="/"+H.replace(/^\//,"").split("/").slice(M.length).join("/")}let E=D0(n,{pathname:b});qt(v||E!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),qt(E==null||E[E.length-1].route.element!==void 0||E[E.length-1].route.Component!==void 0||E[E.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let T=Qp(E&&E.map(M=>Object.assign({},M,{params:Object.assign({},g,M.params),pathname:dl([x,f.encodeLocation?f.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?x:dl([x,f.encodeLocation?f.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),m,o,u);return c&&T?R.createElement(fi.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},T):T}function Vp(){let n=$p(),c=_p(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),o=n instanceof Error?n.stack:null,u="rgba(200,200,200, 0.5)",f={padding:"0.5rem",backgroundColor:u},m={padding:"2px 4px",backgroundColor:u},h=null;return console.error("Error handled by React Router default ErrorBoundary:",n),h=R.createElement(R.Fragment,null,R.createElement("p",null,"💿 Hey developer 👋"),R.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",R.createElement("code",{style:m},"ErrorBoundary")," or"," ",R.createElement("code",{style:m},"errorElement")," prop on your route.")),R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},c),o?R.createElement("pre",{style:f},o):null,h)}var Gp=R.createElement(Vp,null),Xp=class extends R.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,c){return c.location!==n.location||c.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:c.error,location:c.location,revalidation:n.revalidation||c.revalidation}}componentDidCatch(n,c){console.error("React Router caught the following error during render",n,c)}render(){return this.state.error!==void 0?R.createElement(Vt.Provider,{value:this.props.routeContext},R.createElement(Zu.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Jp({routeContext:n,match:c,children:o}){let u=R.useContext(tn);return u&&u.static&&u.staticContext&&(c.route.errorElement||c.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=c.route.id),R.createElement(Vt.Provider,{value:n},o)}function Qp(n,c=[],o=null,u=null){if(n==null){if(!o)return null;if(o.errors)n=o.matches;else if(c.length===0&&!o.initialized&&o.matches.length>0)n=o.matches;else return null}let f=n,m=o==null?void 0:o.errors;if(m!=null){let y=f.findIndex(x=>x.route.id&&(m==null?void 0:m[x.route.id])!==void 0);Le(y>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(m).join(",")}`),f=f.slice(0,Math.min(f.length,y+1))}let h=!1,g=-1;if(o)for(let y=0;y<f.length;y++){let x=f[y];if((x.route.HydrateFallback||x.route.hydrateFallbackElement)&&(g=y),x.route.id){let{loaderData:v,errors:N}=o,S=x.route.loader&&!v.hasOwnProperty(x.route.id)&&(!N||N[x.route.id]===void 0);if(x.route.lazy||S){h=!0,g>=0?f=f.slice(0,g+1):f=[f[0]];break}}}return f.reduceRight((y,x,v)=>{let N,S=!1,H=null,b=null;o&&(N=m&&x.route.id?m[x.route.id]:void 0,H=x.route.errorElement||Gp,h&&(g<0&&v===0?(Y0("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,b=null):g===v&&(S=!0,b=x.route.hydrateFallbackElement||null)));let E=c.concat(f.slice(0,v+1)),T=()=>{let z;return N?z=H:S?z=b:x.route.Component?z=R.createElement(x.route.Component,null):x.route.element?z=x.route.element:z=y,R.createElement(Jp,{match:x,routeContext:{outlet:y,matches:E,isDataRoute:o!=null},children:z})};return o&&(x.route.ErrorBoundary||x.route.errorElement||v===0)?R.createElement(Xp,{location:o.location,revalidation:o.revalidation,component:H,error:N,children:T(),routeContext:{outlet:null,matches:E,isDataRoute:!0}}):T()},null)}function Ku(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function kp(n){let c=R.useContext(tn);return Le(c,Ku(n)),c}function Zp(n){let c=R.useContext(Xs);return Le(c,Ku(n)),c}function Kp(n){let c=R.useContext(Vt);return Le(c,Ku(n)),c}function Fu(n){let c=Kp(n),o=c.matches[c.matches.length-1];return Le(o.route.id,`${n} can only be used on routes that contain a unique "id"`),o.route.id}function Fp(){return Fu("useRouteId")}function $p(){var u;let n=R.useContext(Zu),c=Zp("useRouteError"),o=Fu("useRouteError");return n!==void 0?n:(u=c.errors)==null?void 0:u[o]}function Pp(){let{router:n}=kp("useNavigate"),c=Fu("useNavigate"),o=R.useRef(!1);return B0(()=>{o.current=!0}),R.useCallback(async(f,m={})=>{qt(o.current,H0),o.current&&(typeof f=="number"?n.navigate(f):await n.navigate(f,{fromRouteId:c,...m}))},[n,c])}var d0={};function Y0(n,c,o){!c&&!d0[n]&&(d0[n]=!0,qt(!1,o))}R.memo(Wp);function Wp({routes:n,future:c,state:o}){return q0(n,void 0,o,c)}function zu({to:n,replace:c,state:o,relative:u}){Le(ln(),"<Navigate> may be used only in the context of a <Router> component.");let{static:f}=R.useContext(Yt);qt(!f,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:m}=R.useContext(Vt),{pathname:h}=Et(),g=Xl(),y=ku(n,Qu(m),h,u==="path"),x=JSON.stringify(y);return R.useEffect(()=>{g(JSON.parse(x),{replace:c,state:o,relative:u})},[g,x,u,c,o]),null}function it(n){Le(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ip({basename:n="/",children:c=null,location:o,navigationType:u="POP",navigator:f,static:m=!1}){Le(!ln(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=n.replace(/^\/*/,"/"),g=R.useMemo(()=>({basename:h,navigator:f,static:m,future:{}}),[h,f,m]);typeof o=="string"&&(o=en(o));let{pathname:y="/",search:x="",hash:v="",state:N=null,key:S="default"}=o,H=R.useMemo(()=>{let b=hl(y,h);return b==null?null:{location:{pathname:b,search:x,hash:v,state:N,key:S},navigationType:u}},[h,y,x,v,N,S,u]);return qt(H!=null,`<Router basename="${h}"> is not able to match the URL "${y}${x}${v}" because it does not start with the basename, so the <Router> won't render anything.`),H==null?null:R.createElement(Yt.Provider,{value:g},R.createElement(fi.Provider,{children:c,value:H}))}function ex({children:n,location:c}){return Yp(Ou(n),c)}function Ou(n,c=[]){let o=[];return R.Children.forEach(n,(u,f)=>{if(!R.isValidElement(u))return;let m=[...c,f];if(u.type===R.Fragment){o.push.apply(o,Ou(u.props.children,m));return}Le(u.type===it,`[${typeof u.type=="string"?u.type:u.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Le(!u.props.index||!u.props.children,"An index route cannot have child routes.");let h={id:u.props.id||m.join("-"),caseSensitive:u.props.caseSensitive,element:u.props.element,Component:u.props.Component,index:u.props.index,path:u.props.path,loader:u.props.loader,action:u.props.action,hydrateFallbackElement:u.props.hydrateFallbackElement,HydrateFallback:u.props.HydrateFallback,errorElement:u.props.errorElement,ErrorBoundary:u.props.ErrorBoundary,hasErrorBoundary:u.props.hasErrorBoundary===!0||u.props.ErrorBoundary!=null||u.props.errorElement!=null,shouldRevalidate:u.props.shouldRevalidate,handle:u.props.handle,lazy:u.props.lazy};u.props.children&&(h.children=Ou(u.props.children,m)),o.push(h)}),o}var Ms="get",_s="application/x-www-form-urlencoded";function Qs(n){return n!=null&&typeof n.tagName=="string"}function tx(n){return Qs(n)&&n.tagName.toLowerCase()==="button"}function lx(n){return Qs(n)&&n.tagName.toLowerCase()==="form"}function ax(n){return Qs(n)&&n.tagName.toLowerCase()==="input"}function nx(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function ix(n,c){return n.button===0&&(!c||c==="_self")&&!nx(n)}var Ds=null;function sx(){if(Ds===null)try{new FormData(document.createElement("form"),0),Ds=!1}catch{Ds=!0}return Ds}var rx=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Su(n){return n!=null&&!rx.has(n)?(qt(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${_s}"`),null):n}function cx(n,c){let o,u,f,m,h;if(lx(n)){let g=n.getAttribute("action");u=g?hl(g,c):null,o=n.getAttribute("method")||Ms,f=Su(n.getAttribute("enctype"))||_s,m=new FormData(n)}else if(tx(n)||ax(n)&&(n.type==="submit"||n.type==="image")){let g=n.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let y=n.getAttribute("formaction")||g.getAttribute("action");if(u=y?hl(y,c):null,o=n.getAttribute("formmethod")||g.getAttribute("method")||Ms,f=Su(n.getAttribute("formenctype"))||Su(g.getAttribute("enctype"))||_s,m=new FormData(g,n),!sx()){let{name:x,type:v,value:N}=n;if(v==="image"){let S=x?`${x}.`:"";m.append(`${S}x`,"0"),m.append(`${S}y`,"0")}else x&&m.append(x,N)}}else{if(Qs(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=Ms,u=null,f=_s,h=n}return m&&f==="text/plain"&&(h=m,m=void 0),{action:u,method:o.toLowerCase(),encType:f,formData:m,body:h}}function $u(n,c){if(n===!1||n===null||typeof n>"u")throw new Error(c)}async function ux(n,c){if(n.id in c)return c[n.id];try{let o=await import(n.module);return c[n.id]=o,o}catch(o){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ox(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function fx(n,c,o){let u=await Promise.all(n.map(async f=>{let m=c.routes[f.route.id];if(m){let h=await ux(m,o);return h.links?h.links():[]}return[]}));return px(u.flat(1).filter(ox).filter(f=>f.rel==="stylesheet"||f.rel==="preload").map(f=>f.rel==="stylesheet"?{...f,rel:"prefetch",as:"style"}:{...f,rel:"prefetch"}))}function m0(n,c,o,u,f,m){let h=(y,x)=>o[x]?y.route.id!==o[x].route.id:!0,g=(y,x)=>{var v;return o[x].pathname!==y.pathname||((v=o[x].route.path)==null?void 0:v.endsWith("*"))&&o[x].params["*"]!==y.params["*"]};return m==="assets"?c.filter((y,x)=>h(y,x)||g(y,x)):m==="data"?c.filter((y,x)=>{var N;let v=u.routes[y.route.id];if(!v||!v.hasLoader)return!1;if(h(y,x)||g(y,x))return!0;if(y.route.shouldRevalidate){let S=y.route.shouldRevalidate({currentUrl:new URL(f.pathname+f.search+f.hash,window.origin),currentParams:((N=o[0])==null?void 0:N.params)||{},nextUrl:new URL(n,window.origin),nextParams:y.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function dx(n,c,{includeHydrateFallback:o}={}){return mx(n.map(u=>{let f=c.routes[u.route.id];if(!f)return[];let m=[f.module];return f.clientActionModule&&(m=m.concat(f.clientActionModule)),f.clientLoaderModule&&(m=m.concat(f.clientLoaderModule)),o&&f.hydrateFallbackModule&&(m=m.concat(f.hydrateFallbackModule)),f.imports&&(m=m.concat(f.imports)),m}).flat(1))}function mx(n){return[...new Set(n)]}function hx(n){let c={},o=Object.keys(n).sort();for(let u of o)c[u]=n[u];return c}function px(n,c){let o=new Set;return new Set(c),n.reduce((u,f)=>{let m=JSON.stringify(hx(f));return o.has(m)||(o.add(m),u.push({key:m,link:f})),u},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var xx=new Set([100,101,204,205]);function yx(n,c){let o=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return o.pathname==="/"?o.pathname="_root.data":c&&hl(o.pathname,c)==="/"?o.pathname=`${c.replace(/\/$/,"")}/_root.data`:o.pathname=`${o.pathname.replace(/\/$/,"")}.data`,o}function V0(){let n=R.useContext(tn);return $u(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function gx(){let n=R.useContext(Xs);return $u(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Pu=R.createContext(void 0);Pu.displayName="FrameworkContext";function G0(){let n=R.useContext(Pu);return $u(n,"You must render this element inside a <HydratedRouter> element"),n}function vx(n,c){let o=R.useContext(Pu),[u,f]=R.useState(!1),[m,h]=R.useState(!1),{onFocus:g,onBlur:y,onMouseEnter:x,onMouseLeave:v,onTouchStart:N}=c,S=R.useRef(null);R.useEffect(()=>{if(n==="render"&&h(!0),n==="viewport"){let E=z=>{z.forEach(M=>{h(M.isIntersecting)})},T=new IntersectionObserver(E,{threshold:.5});return S.current&&T.observe(S.current),()=>{T.disconnect()}}},[n]),R.useEffect(()=>{if(u){let E=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(E)}}},[u]);let H=()=>{f(!0)},b=()=>{f(!1),h(!1)};return o?n!=="intent"?[m,S,{}]:[m,S,{onFocus:si(g,H),onBlur:si(y,b),onMouseEnter:si(x,H),onMouseLeave:si(v,b),onTouchStart:si(N,H)}]:[!1,S,{}]}function si(n,c){return o=>{n&&n(o),o.defaultPrevented||c(o)}}function bx({page:n,...c}){let{router:o}=V0(),u=R.useMemo(()=>D0(o.routes,n,o.basename),[o.routes,n,o.basename]);return u?R.createElement(Nx,{page:n,matches:u,...c}):null}function jx(n){let{manifest:c,routeModules:o}=G0(),[u,f]=R.useState([]);return R.useEffect(()=>{let m=!1;return fx(n,c,o).then(h=>{m||f(h)}),()=>{m=!0}},[n,c,o]),u}function Nx({page:n,matches:c,...o}){let u=Et(),{manifest:f,routeModules:m}=G0(),{basename:h}=V0(),{loaderData:g,matches:y}=gx(),x=R.useMemo(()=>m0(n,c,y,f,u,"data"),[n,c,y,f,u]),v=R.useMemo(()=>m0(n,c,y,f,u,"assets"),[n,c,y,f,u]),N=R.useMemo(()=>{if(n===u.pathname+u.search+u.hash)return[];let b=new Set,E=!1;if(c.forEach(z=>{var J;let M=f.routes[z.route.id];!M||!M.hasLoader||(!x.some(k=>k.route.id===z.route.id)&&z.route.id in g&&((J=m[z.route.id])!=null&&J.shouldRevalidate)||M.hasClientLoader?E=!0:b.add(z.route.id))}),b.size===0)return[];let T=yx(n,h);return E&&b.size>0&&T.searchParams.set("_routes",c.filter(z=>b.has(z.route.id)).map(z=>z.route.id).join(",")),[T.pathname+T.search]},[h,g,u,f,x,c,n,m]),S=R.useMemo(()=>dx(v,f),[v,f]),H=jx(v);return R.createElement(R.Fragment,null,N.map(b=>R.createElement("link",{key:b,rel:"prefetch",as:"fetch",href:b,...o})),S.map(b=>R.createElement("link",{key:b,rel:"modulepreload",href:b,...o})),H.map(({key:b,link:E})=>R.createElement("link",{key:b,...E})))}function Sx(...n){return c=>{n.forEach(o=>{typeof o=="function"?o(c):o!=null&&(o.current=c)})}}var X0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{X0&&(window.__reactRouterVersion="7.6.1")}catch{}function Ex({basename:n,children:c,window:o}){let u=R.useRef();u.current==null&&(u.current=fp({window:o,v5Compat:!0}));let f=u.current,[m,h]=R.useState({action:f.action,location:f.location}),g=R.useCallback(y=>{R.startTransition(()=>h(y))},[h]);return R.useLayoutEffect(()=>f.listen(g),[f,g]),R.createElement(Ip,{basename:n,children:c,location:m.location,navigationType:m.action,navigator:f})}var J0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,he=R.forwardRef(function({onClick:c,discover:o="render",prefetch:u="none",relative:f,reloadDocument:m,replace:h,state:g,target:y,to:x,preventScrollReset:v,viewTransition:N,...S},H){let{basename:b}=R.useContext(Yt),E=typeof x=="string"&&J0.test(x),T,z=!1;if(typeof x=="string"&&E&&(T=x,X0))try{let oe=new URL(window.location.href),ze=x.startsWith("//")?new URL(oe.protocol+x):new URL(x),ee=hl(ze.pathname,b);ze.origin===oe.origin&&ee!=null?x=ee+ze.search+ze.hash:z=!0}catch{qt(!1,`<Link to="${x}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let M=Bp(x,{relative:f}),[J,k,Q]=vx(u,S),Z=Tx(x,{replace:h,state:g,target:y,preventScrollReset:v,relative:f,viewTransition:N});function le(oe){c&&c(oe),oe.defaultPrevented||Z(oe)}let xe=R.createElement("a",{...S,...Q,href:T||M,onClick:z||m?c:le,ref:Sx(H,k),target:y,"data-discover":!E&&o==="render"?"true":void 0});return J&&!E?R.createElement(R.Fragment,null,xe,R.createElement(bx,{page:M})):xe});he.displayName="Link";var fl=R.forwardRef(function({"aria-current":c="page",caseSensitive:o=!1,className:u="",end:f=!1,style:m,to:h,viewTransition:g,children:y,...x},v){let N=di(h,{relative:x.relative}),S=Et(),H=R.useContext(Xs),{navigator:b,basename:E}=R.useContext(Yt),T=H!=null&&Dx(N)&&g===!0,z=b.encodeLocation?b.encodeLocation(N).pathname:N.pathname,M=S.pathname,J=H&&H.navigation&&H.navigation.location?H.navigation.location.pathname:null;o||(M=M.toLowerCase(),J=J?J.toLowerCase():null,z=z.toLowerCase()),J&&E&&(J=hl(J,E)||J);const k=z!=="/"&&z.endsWith("/")?z.length-1:z.length;let Q=M===z||!f&&M.startsWith(z)&&M.charAt(k)==="/",Z=J!=null&&(J===z||!f&&J.startsWith(z)&&J.charAt(z.length)==="/"),le={isActive:Q,isPending:Z,isTransitioning:T},xe=Q?c:void 0,oe;typeof u=="function"?oe=u(le):oe=[u,Q?"active":null,Z?"pending":null,T?"transitioning":null].filter(Boolean).join(" ");let ze=typeof m=="function"?m(le):m;return R.createElement(he,{...x,"aria-current":xe,className:oe,ref:v,style:ze,to:h,viewTransition:g},typeof y=="function"?y(le):y)});fl.displayName="NavLink";var wx=R.forwardRef(({discover:n="render",fetcherKey:c,navigate:o,reloadDocument:u,replace:f,state:m,method:h=Ms,action:g,onSubmit:y,relative:x,preventScrollReset:v,viewTransition:N,...S},H)=>{let b=zx(),E=Ox(g,{relative:x}),T=h.toLowerCase()==="get"?"get":"post",z=typeof g=="string"&&J0.test(g),M=J=>{if(y&&y(J),J.defaultPrevented)return;J.preventDefault();let k=J.nativeEvent.submitter,Q=(k==null?void 0:k.getAttribute("formmethod"))||h;b(k||J.currentTarget,{fetcherKey:c,method:Q,navigate:o,replace:f,state:m,relative:x,preventScrollReset:v,viewTransition:N})};return R.createElement("form",{ref:H,method:T,action:E,onSubmit:u?y:M,...S,"data-discover":!z&&n==="render"?"true":void 0})});wx.displayName="Form";function Ax(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Q0(n){let c=R.useContext(tn);return Le(c,Ax(n)),c}function Tx(n,{target:c,replace:o,state:u,preventScrollReset:f,relative:m,viewTransition:h}={}){let g=Xl(),y=Et(),x=di(n,{relative:m});return R.useCallback(v=>{if(ix(v,c)){v.preventDefault();let N=o!==void 0?o:ci(y)===ci(x);g(n,{replace:N,state:u,preventScrollReset:f,relative:m,viewTransition:h})}},[y,g,x,o,u,c,n,f,m,h])}var Rx=0,Cx=()=>`__${String(++Rx)}__`;function zx(){let{router:n}=Q0("useSubmit"),{basename:c}=R.useContext(Yt),o=Fp();return R.useCallback(async(u,f={})=>{let{action:m,method:h,encType:g,formData:y,body:x}=cx(u,c);if(f.navigate===!1){let v=f.fetcherKey||Cx();await n.fetch(v,o,f.action||m,{preventScrollReset:f.preventScrollReset,formData:y,body:x,formMethod:f.method||h,formEncType:f.encType||g,flushSync:f.flushSync})}else await n.navigate(f.action||m,{preventScrollReset:f.preventScrollReset,formData:y,body:x,formMethod:f.method||h,formEncType:f.encType||g,replace:f.replace,state:f.state,fromRouteId:o,flushSync:f.flushSync,viewTransition:f.viewTransition})},[n,c,o])}function Ox(n,{relative:c}={}){let{basename:o}=R.useContext(Yt),u=R.useContext(Vt);Le(u,"useFormAction must be used inside a RouteContext");let[f]=u.matches.slice(-1),m={...di(n||".",{relative:c})},h=Et();if(n==null){m.search=h.search;let g=new URLSearchParams(m.search),y=g.getAll("index");if(y.some(v=>v==="")){g.delete("index"),y.filter(N=>N).forEach(N=>g.append("index",N));let v=g.toString();m.search=v?`?${v}`:""}}return(!n||n===".")&&f.route.index&&(m.search=m.search?m.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(m.pathname=m.pathname==="/"?o:dl([o,m.pathname])),ci(m)}function Dx(n,c={}){let o=R.useContext(L0);Le(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:u}=Q0("useViewTransitionState"),f=di(n,{relative:c.relative});if(!o.isTransitioning)return!1;let m=hl(o.currentLocation.pathname,u)||o.currentLocation.pathname,h=hl(o.nextLocation.pathname,u)||o.nextLocation.pathname;return Bs(f.pathname,h)!=null||Bs(f.pathname,m)!=null}[...xx];function Du(n){this.message=n}Du.prototype=new Error,Du.prototype.name="InvalidCharacterError";var h0=typeof window<"u"&&window.atob&&window.atob.bind(window)||function(n){var c=String(n).replace(/=+$/,"");if(c.length%4==1)throw new Du("'atob' failed: The string to be decoded is not correctly encoded.");for(var o,u,f=0,m=0,h="";u=c.charAt(m++);~u&&(o=f%4?64*o+u:u,f++%4)?h+=String.fromCharCode(255&o>>(-2*f&6)):0)u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(u);return h};function Mx(n){var c=n.replace(/-/g,"+").replace(/_/g,"/");switch(c.length%4){case 0:break;case 2:c+="==";break;case 3:c+="=";break;default:throw"Illegal base64url string!"}try{return function(o){return decodeURIComponent(h0(o).replace(/(.)/g,function(u,f){var m=f.charCodeAt(0).toString(16).toUpperCase();return m.length<2&&(m="0"+m),"%"+m}))}(c)}catch{return h0(c)}}function qs(n){this.message=n}function _x(n,c){if(typeof n!="string")throw new qs("Invalid token specified");var o=(c=c||{}).header===!0?0:1;try{return JSON.parse(Mx(n.split(".")[o]))}catch(u){throw new qs("Invalid token specified: "+u.message)}}qs.prototype=new Error,qs.prototype.name="InvalidTokenError";function k0(n,c){return function(){return n.apply(c,arguments)}}const{toString:Ux}=Object.prototype,{getPrototypeOf:Wu}=Object,{iterator:ks,toStringTag:Z0}=Symbol,Zs=(n=>c=>{const o=Ux.call(c);return n[o]||(n[o]=o.slice(8,-1).toLowerCase())})(Object.create(null)),Gt=n=>(n=n.toLowerCase(),c=>Zs(c)===n),Ks=n=>c=>typeof c===n,{isArray:an}=Array,ui=Ks("undefined");function Lx(n){return n!==null&&!ui(n)&&n.constructor!==null&&!ui(n.constructor)&&ft(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const K0=Gt("ArrayBuffer");function Hx(n){let c;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?c=ArrayBuffer.isView(n):c=n&&n.buffer&&K0(n.buffer),c}const Bx=Ks("string"),ft=Ks("function"),F0=Ks("number"),Fs=n=>n!==null&&typeof n=="object",qx=n=>n===!0||n===!1,Us=n=>{if(Zs(n)!=="object")return!1;const c=Wu(n);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Z0 in n)&&!(ks in n)},Yx=Gt("Date"),Vx=Gt("File"),Gx=Gt("Blob"),Xx=Gt("FileList"),Jx=n=>Fs(n)&&ft(n.pipe),Qx=n=>{let c;return n&&(typeof FormData=="function"&&n instanceof FormData||ft(n.append)&&((c=Zs(n))==="formdata"||c==="object"&&ft(n.toString)&&n.toString()==="[object FormData]"))},kx=Gt("URLSearchParams"),[Zx,Kx,Fx,$x]=["ReadableStream","Request","Response","Headers"].map(Gt),Px=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function mi(n,c,{allOwnKeys:o=!1}={}){if(n===null||typeof n>"u")return;let u,f;if(typeof n!="object"&&(n=[n]),an(n))for(u=0,f=n.length;u<f;u++)c.call(null,n[u],u,n);else{const m=o?Object.getOwnPropertyNames(n):Object.keys(n),h=m.length;let g;for(u=0;u<h;u++)g=m[u],c.call(null,n[g],g,n)}}function $0(n,c){c=c.toLowerCase();const o=Object.keys(n);let u=o.length,f;for(;u-- >0;)if(f=o[u],c===f.toLowerCase())return f;return null}const ua=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,P0=n=>!ui(n)&&n!==ua;function Mu(){const{caseless:n}=P0(this)&&this||{},c={},o=(u,f)=>{const m=n&&$0(c,f)||f;Us(c[m])&&Us(u)?c[m]=Mu(c[m],u):Us(u)?c[m]=Mu({},u):an(u)?c[m]=u.slice():c[m]=u};for(let u=0,f=arguments.length;u<f;u++)arguments[u]&&mi(arguments[u],o);return c}const Wx=(n,c,o,{allOwnKeys:u}={})=>(mi(c,(f,m)=>{o&&ft(f)?n[m]=k0(f,o):n[m]=f},{allOwnKeys:u}),n),Ix=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),ey=(n,c,o,u)=>{n.prototype=Object.create(c.prototype,u),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:c.prototype}),o&&Object.assign(n.prototype,o)},ty=(n,c,o,u)=>{let f,m,h;const g={};if(c=c||{},n==null)return c;do{for(f=Object.getOwnPropertyNames(n),m=f.length;m-- >0;)h=f[m],(!u||u(h,n,c))&&!g[h]&&(c[h]=n[h],g[h]=!0);n=o!==!1&&Wu(n)}while(n&&(!o||o(n,c))&&n!==Object.prototype);return c},ly=(n,c,o)=>{n=String(n),(o===void 0||o>n.length)&&(o=n.length),o-=c.length;const u=n.indexOf(c,o);return u!==-1&&u===o},ay=n=>{if(!n)return null;if(an(n))return n;let c=n.length;if(!F0(c))return null;const o=new Array(c);for(;c-- >0;)o[c]=n[c];return o},ny=(n=>c=>n&&c instanceof n)(typeof Uint8Array<"u"&&Wu(Uint8Array)),iy=(n,c)=>{const u=(n&&n[ks]).call(n);let f;for(;(f=u.next())&&!f.done;){const m=f.value;c.call(n,m[0],m[1])}},sy=(n,c)=>{let o;const u=[];for(;(o=n.exec(c))!==null;)u.push(o);return u},ry=Gt("HTMLFormElement"),cy=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(o,u,f){return u.toUpperCase()+f}),p0=(({hasOwnProperty:n})=>(c,o)=>n.call(c,o))(Object.prototype),uy=Gt("RegExp"),W0=(n,c)=>{const o=Object.getOwnPropertyDescriptors(n),u={};mi(o,(f,m)=>{let h;(h=c(f,m,n))!==!1&&(u[m]=h||f)}),Object.defineProperties(n,u)},oy=n=>{W0(n,(c,o)=>{if(ft(n)&&["arguments","caller","callee"].indexOf(o)!==-1)return!1;const u=n[o];if(ft(u)){if(c.enumerable=!1,"writable"in c){c.writable=!1;return}c.set||(c.set=()=>{throw Error("Can not rewrite read-only method '"+o+"'")})}})},fy=(n,c)=>{const o={},u=f=>{f.forEach(m=>{o[m]=!0})};return an(n)?u(n):u(String(n).split(c)),o},dy=()=>{},my=(n,c)=>n!=null&&Number.isFinite(n=+n)?n:c;function hy(n){return!!(n&&ft(n.append)&&n[Z0]==="FormData"&&n[ks])}const py=n=>{const c=new Array(10),o=(u,f)=>{if(Fs(u)){if(c.indexOf(u)>=0)return;if(!("toJSON"in u)){c[f]=u;const m=an(u)?[]:{};return mi(u,(h,g)=>{const y=o(h,f+1);!ui(y)&&(m[g]=y)}),c[f]=void 0,m}}return u};return o(n,0)},xy=Gt("AsyncFunction"),yy=n=>n&&(Fs(n)||ft(n))&&ft(n.then)&&ft(n.catch),I0=((n,c)=>n?setImmediate:c?((o,u)=>(ua.addEventListener("message",({source:f,data:m})=>{f===ua&&m===o&&u.length&&u.shift()()},!1),f=>{u.push(f),ua.postMessage(o,"*")}))(`axios@${Math.random()}`,[]):o=>setTimeout(o))(typeof setImmediate=="function",ft(ua.postMessage)),gy=typeof queueMicrotask<"u"?queueMicrotask.bind(ua):typeof process<"u"&&process.nextTick||I0,vy=n=>n!=null&&ft(n[ks]),B={isArray:an,isArrayBuffer:K0,isBuffer:Lx,isFormData:Qx,isArrayBufferView:Hx,isString:Bx,isNumber:F0,isBoolean:qx,isObject:Fs,isPlainObject:Us,isReadableStream:Zx,isRequest:Kx,isResponse:Fx,isHeaders:$x,isUndefined:ui,isDate:Yx,isFile:Vx,isBlob:Gx,isRegExp:uy,isFunction:ft,isStream:Jx,isURLSearchParams:kx,isTypedArray:ny,isFileList:Xx,forEach:mi,merge:Mu,extend:Wx,trim:Px,stripBOM:Ix,inherits:ey,toFlatObject:ty,kindOf:Zs,kindOfTest:Gt,endsWith:ly,toArray:ay,forEachEntry:iy,matchAll:sy,isHTMLForm:ry,hasOwnProperty:p0,hasOwnProp:p0,reduceDescriptors:W0,freezeMethods:oy,toObjectSet:fy,toCamelCase:cy,noop:dy,toFiniteNumber:my,findKey:$0,global:ua,isContextDefined:P0,isSpecCompliantForm:hy,toJSONObject:py,isAsyncFn:xy,isThenable:yy,setImmediate:I0,asap:gy,isIterable:vy};function de(n,c,o,u,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",c&&(this.code=c),o&&(this.config=o),u&&(this.request=u),f&&(this.response=f,this.status=f.status?f.status:null)}B.inherits(de,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:B.toJSONObject(this.config),code:this.code,status:this.status}}});const eh=de.prototype,th={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{th[n]={value:n}});Object.defineProperties(de,th);Object.defineProperty(eh,"isAxiosError",{value:!0});de.from=(n,c,o,u,f,m)=>{const h=Object.create(eh);return B.toFlatObject(n,h,function(y){return y!==Error.prototype},g=>g!=="isAxiosError"),de.call(h,n.message,c,o,u,f),h.cause=n,h.name=n.name,m&&Object.assign(h,m),h};const by=null;function _u(n){return B.isPlainObject(n)||B.isArray(n)}function lh(n){return B.endsWith(n,"[]")?n.slice(0,-2):n}function x0(n,c,o){return n?n.concat(c).map(function(f,m){return f=lh(f),!o&&m?"["+f+"]":f}).join(o?".":""):c}function jy(n){return B.isArray(n)&&!n.some(_u)}const Ny=B.toFlatObject(B,{},null,function(c){return/^is[A-Z]/.test(c)});function $s(n,c,o){if(!B.isObject(n))throw new TypeError("target must be an object");c=c||new FormData,o=B.toFlatObject(o,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,T){return!B.isUndefined(T[E])});const u=o.metaTokens,f=o.visitor||v,m=o.dots,h=o.indexes,y=(o.Blob||typeof Blob<"u"&&Blob)&&B.isSpecCompliantForm(c);if(!B.isFunction(f))throw new TypeError("visitor must be a function");function x(b){if(b===null)return"";if(B.isDate(b))return b.toISOString();if(!y&&B.isBlob(b))throw new de("Blob is not supported. Use a Buffer instead.");return B.isArrayBuffer(b)||B.isTypedArray(b)?y&&typeof Blob=="function"?new Blob([b]):Buffer.from(b):b}function v(b,E,T){let z=b;if(b&&!T&&typeof b=="object"){if(B.endsWith(E,"{}"))E=u?E:E.slice(0,-2),b=JSON.stringify(b);else if(B.isArray(b)&&jy(b)||(B.isFileList(b)||B.endsWith(E,"[]"))&&(z=B.toArray(b)))return E=lh(E),z.forEach(function(J,k){!(B.isUndefined(J)||J===null)&&c.append(h===!0?x0([E],k,m):h===null?E:E+"[]",x(J))}),!1}return _u(b)?!0:(c.append(x0(T,E,m),x(b)),!1)}const N=[],S=Object.assign(Ny,{defaultVisitor:v,convertValue:x,isVisitable:_u});function H(b,E){if(!B.isUndefined(b)){if(N.indexOf(b)!==-1)throw Error("Circular reference detected in "+E.join("."));N.push(b),B.forEach(b,function(z,M){(!(B.isUndefined(z)||z===null)&&f.call(c,z,B.isString(M)?M.trim():M,E,S))===!0&&H(z,E?E.concat(M):[M])}),N.pop()}}if(!B.isObject(n))throw new TypeError("data must be an object");return H(n),c}function y0(n){const c={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(u){return c[u]})}function Iu(n,c){this._pairs=[],n&&$s(n,this,c)}const ah=Iu.prototype;ah.append=function(c,o){this._pairs.push([c,o])};ah.toString=function(c){const o=c?function(u){return c.call(this,u,y0)}:y0;return this._pairs.map(function(f){return o(f[0])+"="+o(f[1])},"").join("&")};function Sy(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function nh(n,c,o){if(!c)return n;const u=o&&o.encode||Sy;B.isFunction(o)&&(o={serialize:o});const f=o&&o.serialize;let m;if(f?m=f(c,o):m=B.isURLSearchParams(c)?c.toString():new Iu(c,o).toString(u),m){const h=n.indexOf("#");h!==-1&&(n=n.slice(0,h)),n+=(n.indexOf("?")===-1?"?":"&")+m}return n}class g0{constructor(){this.handlers=[]}use(c,o,u){return this.handlers.push({fulfilled:c,rejected:o,synchronous:u?u.synchronous:!1,runWhen:u?u.runWhen:null}),this.handlers.length-1}eject(c){this.handlers[c]&&(this.handlers[c]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(c){B.forEach(this.handlers,function(u){u!==null&&c(u)})}}const ih={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ey=typeof URLSearchParams<"u"?URLSearchParams:Iu,wy=typeof FormData<"u"?FormData:null,Ay=typeof Blob<"u"?Blob:null,Ty={isBrowser:!0,classes:{URLSearchParams:Ey,FormData:wy,Blob:Ay},protocols:["http","https","file","blob","url","data"]},eo=typeof window<"u"&&typeof document<"u",Uu=typeof navigator=="object"&&navigator||void 0,Ry=eo&&(!Uu||["ReactNative","NativeScript","NS"].indexOf(Uu.product)<0),Cy=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",zy=eo&&window.location.href||"http://localhost",Oy=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:eo,hasStandardBrowserEnv:Ry,hasStandardBrowserWebWorkerEnv:Cy,navigator:Uu,origin:zy},Symbol.toStringTag,{value:"Module"})),at={...Oy,...Ty};function Dy(n,c){return $s(n,new at.classes.URLSearchParams,Object.assign({visitor:function(o,u,f,m){return at.isNode&&B.isBuffer(o)?(this.append(u,o.toString("base64")),!1):m.defaultVisitor.apply(this,arguments)}},c))}function My(n){return B.matchAll(/\w+|\[(\w*)]/g,n).map(c=>c[0]==="[]"?"":c[1]||c[0])}function _y(n){const c={},o=Object.keys(n);let u;const f=o.length;let m;for(u=0;u<f;u++)m=o[u],c[m]=n[m];return c}function sh(n){function c(o,u,f,m){let h=o[m++];if(h==="__proto__")return!0;const g=Number.isFinite(+h),y=m>=o.length;return h=!h&&B.isArray(f)?f.length:h,y?(B.hasOwnProp(f,h)?f[h]=[f[h],u]:f[h]=u,!g):((!f[h]||!B.isObject(f[h]))&&(f[h]=[]),c(o,u,f[h],m)&&B.isArray(f[h])&&(f[h]=_y(f[h])),!g)}if(B.isFormData(n)&&B.isFunction(n.entries)){const o={};return B.forEachEntry(n,(u,f)=>{c(My(u),f,o,0)}),o}return null}function Uy(n,c,o){if(B.isString(n))try{return(c||JSON.parse)(n),B.trim(n)}catch(u){if(u.name!=="SyntaxError")throw u}return(o||JSON.stringify)(n)}const hi={transitional:ih,adapter:["xhr","http","fetch"],transformRequest:[function(c,o){const u=o.getContentType()||"",f=u.indexOf("application/json")>-1,m=B.isObject(c);if(m&&B.isHTMLForm(c)&&(c=new FormData(c)),B.isFormData(c))return f?JSON.stringify(sh(c)):c;if(B.isArrayBuffer(c)||B.isBuffer(c)||B.isStream(c)||B.isFile(c)||B.isBlob(c)||B.isReadableStream(c))return c;if(B.isArrayBufferView(c))return c.buffer;if(B.isURLSearchParams(c))return o.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),c.toString();let g;if(m){if(u.indexOf("application/x-www-form-urlencoded")>-1)return Dy(c,this.formSerializer).toString();if((g=B.isFileList(c))||u.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return $s(g?{"files[]":c}:c,y&&new y,this.formSerializer)}}return m||f?(o.setContentType("application/json",!1),Uy(c)):c}],transformResponse:[function(c){const o=this.transitional||hi.transitional,u=o&&o.forcedJSONParsing,f=this.responseType==="json";if(B.isResponse(c)||B.isReadableStream(c))return c;if(c&&B.isString(c)&&(u&&!this.responseType||f)){const h=!(o&&o.silentJSONParsing)&&f;try{return JSON.parse(c)}catch(g){if(h)throw g.name==="SyntaxError"?de.from(g,de.ERR_BAD_RESPONSE,this,null,this.response):g}}return c}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(c){return c>=200&&c<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};B.forEach(["delete","get","head","post","put","patch"],n=>{hi.headers[n]={}});const Ly=B.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Hy=n=>{const c={};let o,u,f;return n&&n.split(`
`).forEach(function(h){f=h.indexOf(":"),o=h.substring(0,f).trim().toLowerCase(),u=h.substring(f+1).trim(),!(!o||c[o]&&Ly[o])&&(o==="set-cookie"?c[o]?c[o].push(u):c[o]=[u]:c[o]=c[o]?c[o]+", "+u:u)}),c},v0=Symbol("internals");function ri(n){return n&&String(n).trim().toLowerCase()}function Ls(n){return n===!1||n==null?n:B.isArray(n)?n.map(Ls):String(n)}function By(n){const c=Object.create(null),o=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let u;for(;u=o.exec(n);)c[u[1]]=u[2];return c}const qy=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Eu(n,c,o,u,f){if(B.isFunction(u))return u.call(this,c,o);if(f&&(c=o),!!B.isString(c)){if(B.isString(u))return c.indexOf(u)!==-1;if(B.isRegExp(u))return u.test(c)}}function Yy(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(c,o,u)=>o.toUpperCase()+u)}function Vy(n,c){const o=B.toCamelCase(" "+c);["get","set","has"].forEach(u=>{Object.defineProperty(n,u+o,{value:function(f,m,h){return this[u].call(this,c,f,m,h)},configurable:!0})})}let dt=class{constructor(c){c&&this.set(c)}set(c,o,u){const f=this;function m(g,y,x){const v=ri(y);if(!v)throw new Error("header name must be a non-empty string");const N=B.findKey(f,v);(!N||f[N]===void 0||x===!0||x===void 0&&f[N]!==!1)&&(f[N||y]=Ls(g))}const h=(g,y)=>B.forEach(g,(x,v)=>m(x,v,y));if(B.isPlainObject(c)||c instanceof this.constructor)h(c,o);else if(B.isString(c)&&(c=c.trim())&&!qy(c))h(Hy(c),o);else if(B.isObject(c)&&B.isIterable(c)){let g={},y,x;for(const v of c){if(!B.isArray(v))throw TypeError("Object iterator must return a key-value pair");g[x=v[0]]=(y=g[x])?B.isArray(y)?[...y,v[1]]:[y,v[1]]:v[1]}h(g,o)}else c!=null&&m(o,c,u);return this}get(c,o){if(c=ri(c),c){const u=B.findKey(this,c);if(u){const f=this[u];if(!o)return f;if(o===!0)return By(f);if(B.isFunction(o))return o.call(this,f,u);if(B.isRegExp(o))return o.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(c,o){if(c=ri(c),c){const u=B.findKey(this,c);return!!(u&&this[u]!==void 0&&(!o||Eu(this,this[u],u,o)))}return!1}delete(c,o){const u=this;let f=!1;function m(h){if(h=ri(h),h){const g=B.findKey(u,h);g&&(!o||Eu(u,u[g],g,o))&&(delete u[g],f=!0)}}return B.isArray(c)?c.forEach(m):m(c),f}clear(c){const o=Object.keys(this);let u=o.length,f=!1;for(;u--;){const m=o[u];(!c||Eu(this,this[m],m,c,!0))&&(delete this[m],f=!0)}return f}normalize(c){const o=this,u={};return B.forEach(this,(f,m)=>{const h=B.findKey(u,m);if(h){o[h]=Ls(f),delete o[m];return}const g=c?Yy(m):String(m).trim();g!==m&&delete o[m],o[g]=Ls(f),u[g]=!0}),this}concat(...c){return this.constructor.concat(this,...c)}toJSON(c){const o=Object.create(null);return B.forEach(this,(u,f)=>{u!=null&&u!==!1&&(o[f]=c&&B.isArray(u)?u.join(", "):u)}),o}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([c,o])=>c+": "+o).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(c){return c instanceof this?c:new this(c)}static concat(c,...o){const u=new this(c);return o.forEach(f=>u.set(f)),u}static accessor(c){const u=(this[v0]=this[v0]={accessors:{}}).accessors,f=this.prototype;function m(h){const g=ri(h);u[g]||(Vy(f,h),u[g]=!0)}return B.isArray(c)?c.forEach(m):m(c),this}};dt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);B.reduceDescriptors(dt.prototype,({value:n},c)=>{let o=c[0].toUpperCase()+c.slice(1);return{get:()=>n,set(u){this[o]=u}}});B.freezeMethods(dt);function wu(n,c){const o=this||hi,u=c||o,f=dt.from(u.headers);let m=u.data;return B.forEach(n,function(g){m=g.call(o,m,f.normalize(),c?c.status:void 0)}),f.normalize(),m}function rh(n){return!!(n&&n.__CANCEL__)}function nn(n,c,o){de.call(this,n??"canceled",de.ERR_CANCELED,c,o),this.name="CanceledError"}B.inherits(nn,de,{__CANCEL__:!0});function ch(n,c,o){const u=o.config.validateStatus;!o.status||!u||u(o.status)?n(o):c(new de("Request failed with status code "+o.status,[de.ERR_BAD_REQUEST,de.ERR_BAD_RESPONSE][Math.floor(o.status/100)-4],o.config,o.request,o))}function Gy(n){const c=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return c&&c[1]||""}function Xy(n,c){n=n||10;const o=new Array(n),u=new Array(n);let f=0,m=0,h;return c=c!==void 0?c:1e3,function(y){const x=Date.now(),v=u[m];h||(h=x),o[f]=y,u[f]=x;let N=m,S=0;for(;N!==f;)S+=o[N++],N=N%n;if(f=(f+1)%n,f===m&&(m=(m+1)%n),x-h<c)return;const H=v&&x-v;return H?Math.round(S*1e3/H):void 0}}function Jy(n,c){let o=0,u=1e3/c,f,m;const h=(x,v=Date.now())=>{o=v,f=null,m&&(clearTimeout(m),m=null),n.apply(null,x)};return[(...x)=>{const v=Date.now(),N=v-o;N>=u?h(x,v):(f=x,m||(m=setTimeout(()=>{m=null,h(f)},u-N)))},()=>f&&h(f)]}const Ys=(n,c,o=3)=>{let u=0;const f=Xy(50,250);return Jy(m=>{const h=m.loaded,g=m.lengthComputable?m.total:void 0,y=h-u,x=f(y),v=h<=g;u=h;const N={loaded:h,total:g,progress:g?h/g:void 0,bytes:y,rate:x||void 0,estimated:x&&g&&v?(g-h)/x:void 0,event:m,lengthComputable:g!=null,[c?"download":"upload"]:!0};n(N)},o)},b0=(n,c)=>{const o=n!=null;return[u=>c[0]({lengthComputable:o,total:n,loaded:u}),c[1]]},j0=n=>(...c)=>B.asap(()=>n(...c)),Qy=at.hasStandardBrowserEnv?((n,c)=>o=>(o=new URL(o,at.origin),n.protocol===o.protocol&&n.host===o.host&&(c||n.port===o.port)))(new URL(at.origin),at.navigator&&/(msie|trident)/i.test(at.navigator.userAgent)):()=>!0,ky=at.hasStandardBrowserEnv?{write(n,c,o,u,f,m){const h=[n+"="+encodeURIComponent(c)];B.isNumber(o)&&h.push("expires="+new Date(o).toGMTString()),B.isString(u)&&h.push("path="+u),B.isString(f)&&h.push("domain="+f),m===!0&&h.push("secure"),document.cookie=h.join("; ")},read(n){const c=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return c?decodeURIComponent(c[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Zy(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function Ky(n,c){return c?n.replace(/\/?\/$/,"")+"/"+c.replace(/^\/+/,""):n}function uh(n,c,o){let u=!Zy(c);return n&&(u||o==!1)?Ky(n,c):c}const N0=n=>n instanceof dt?{...n}:n;function fa(n,c){c=c||{};const o={};function u(x,v,N,S){return B.isPlainObject(x)&&B.isPlainObject(v)?B.merge.call({caseless:S},x,v):B.isPlainObject(v)?B.merge({},v):B.isArray(v)?v.slice():v}function f(x,v,N,S){if(B.isUndefined(v)){if(!B.isUndefined(x))return u(void 0,x,N,S)}else return u(x,v,N,S)}function m(x,v){if(!B.isUndefined(v))return u(void 0,v)}function h(x,v){if(B.isUndefined(v)){if(!B.isUndefined(x))return u(void 0,x)}else return u(void 0,v)}function g(x,v,N){if(N in c)return u(x,v);if(N in n)return u(void 0,x)}const y={url:m,method:m,data:m,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:g,headers:(x,v,N)=>f(N0(x),N0(v),N,!0)};return B.forEach(Object.keys(Object.assign({},n,c)),function(v){const N=y[v]||f,S=N(n[v],c[v],v);B.isUndefined(S)&&N!==g||(o[v]=S)}),o}const oh=n=>{const c=fa({},n);let{data:o,withXSRFToken:u,xsrfHeaderName:f,xsrfCookieName:m,headers:h,auth:g}=c;c.headers=h=dt.from(h),c.url=nh(uh(c.baseURL,c.url,c.allowAbsoluteUrls),n.params,n.paramsSerializer),g&&h.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let y;if(B.isFormData(o)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((y=h.getContentType())!==!1){const[x,...v]=y?y.split(";").map(N=>N.trim()).filter(Boolean):[];h.setContentType([x||"multipart/form-data",...v].join("; "))}}if(at.hasStandardBrowserEnv&&(u&&B.isFunction(u)&&(u=u(c)),u||u!==!1&&Qy(c.url))){const x=f&&m&&ky.read(m);x&&h.set(f,x)}return c},Fy=typeof XMLHttpRequest<"u",$y=Fy&&function(n){return new Promise(function(o,u){const f=oh(n);let m=f.data;const h=dt.from(f.headers).normalize();let{responseType:g,onUploadProgress:y,onDownloadProgress:x}=f,v,N,S,H,b;function E(){H&&H(),b&&b(),f.cancelToken&&f.cancelToken.unsubscribe(v),f.signal&&f.signal.removeEventListener("abort",v)}let T=new XMLHttpRequest;T.open(f.method.toUpperCase(),f.url,!0),T.timeout=f.timeout;function z(){if(!T)return;const J=dt.from("getAllResponseHeaders"in T&&T.getAllResponseHeaders()),Q={data:!g||g==="text"||g==="json"?T.responseText:T.response,status:T.status,statusText:T.statusText,headers:J,config:n,request:T};ch(function(le){o(le),E()},function(le){u(le),E()},Q),T=null}"onloadend"in T?T.onloadend=z:T.onreadystatechange=function(){!T||T.readyState!==4||T.status===0&&!(T.responseURL&&T.responseURL.indexOf("file:")===0)||setTimeout(z)},T.onabort=function(){T&&(u(new de("Request aborted",de.ECONNABORTED,n,T)),T=null)},T.onerror=function(){u(new de("Network Error",de.ERR_NETWORK,n,T)),T=null},T.ontimeout=function(){let k=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const Q=f.transitional||ih;f.timeoutErrorMessage&&(k=f.timeoutErrorMessage),u(new de(k,Q.clarifyTimeoutError?de.ETIMEDOUT:de.ECONNABORTED,n,T)),T=null},m===void 0&&h.setContentType(null),"setRequestHeader"in T&&B.forEach(h.toJSON(),function(k,Q){T.setRequestHeader(Q,k)}),B.isUndefined(f.withCredentials)||(T.withCredentials=!!f.withCredentials),g&&g!=="json"&&(T.responseType=f.responseType),x&&([S,b]=Ys(x,!0),T.addEventListener("progress",S)),y&&T.upload&&([N,H]=Ys(y),T.upload.addEventListener("progress",N),T.upload.addEventListener("loadend",H)),(f.cancelToken||f.signal)&&(v=J=>{T&&(u(!J||J.type?new nn(null,n,T):J),T.abort(),T=null)},f.cancelToken&&f.cancelToken.subscribe(v),f.signal&&(f.signal.aborted?v():f.signal.addEventListener("abort",v)));const M=Gy(f.url);if(M&&at.protocols.indexOf(M)===-1){u(new de("Unsupported protocol "+M+":",de.ERR_BAD_REQUEST,n));return}T.send(m||null)})},Py=(n,c)=>{const{length:o}=n=n?n.filter(Boolean):[];if(c||o){let u=new AbortController,f;const m=function(x){if(!f){f=!0,g();const v=x instanceof Error?x:this.reason;u.abort(v instanceof de?v:new nn(v instanceof Error?v.message:v))}};let h=c&&setTimeout(()=>{h=null,m(new de(`timeout ${c} of ms exceeded`,de.ETIMEDOUT))},c);const g=()=>{n&&(h&&clearTimeout(h),h=null,n.forEach(x=>{x.unsubscribe?x.unsubscribe(m):x.removeEventListener("abort",m)}),n=null)};n.forEach(x=>x.addEventListener("abort",m));const{signal:y}=u;return y.unsubscribe=()=>B.asap(g),y}},Wy=function*(n,c){let o=n.byteLength;if(o<c){yield n;return}let u=0,f;for(;u<o;)f=u+c,yield n.slice(u,f),u=f},Iy=async function*(n,c){for await(const o of e4(n))yield*Wy(o,c)},e4=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const c=n.getReader();try{for(;;){const{done:o,value:u}=await c.read();if(o)break;yield u}}finally{await c.cancel()}},S0=(n,c,o,u)=>{const f=Iy(n,c);let m=0,h,g=y=>{h||(h=!0,u&&u(y))};return new ReadableStream({async pull(y){try{const{done:x,value:v}=await f.next();if(x){g(),y.close();return}let N=v.byteLength;if(o){let S=m+=N;o(S)}y.enqueue(new Uint8Array(v))}catch(x){throw g(x),x}},cancel(y){return g(y),f.return()}},{highWaterMark:2})},Ps=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",fh=Ps&&typeof ReadableStream=="function",t4=Ps&&(typeof TextEncoder=="function"?(n=>c=>n.encode(c))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),dh=(n,...c)=>{try{return!!n(...c)}catch{return!1}},l4=fh&&dh(()=>{let n=!1;const c=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!c}),E0=64*1024,Lu=fh&&dh(()=>B.isReadableStream(new Response("").body)),Vs={stream:Lu&&(n=>n.body)};Ps&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(c=>{!Vs[c]&&(Vs[c]=B.isFunction(n[c])?o=>o[c]():(o,u)=>{throw new de(`Response type '${c}' is not supported`,de.ERR_NOT_SUPPORT,u)})})})(new Response);const a4=async n=>{if(n==null)return 0;if(B.isBlob(n))return n.size;if(B.isSpecCompliantForm(n))return(await new Request(at.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(B.isArrayBufferView(n)||B.isArrayBuffer(n))return n.byteLength;if(B.isURLSearchParams(n)&&(n=n+""),B.isString(n))return(await t4(n)).byteLength},n4=async(n,c)=>{const o=B.toFiniteNumber(n.getContentLength());return o??a4(c)},i4=Ps&&(async n=>{let{url:c,method:o,data:u,signal:f,cancelToken:m,timeout:h,onDownloadProgress:g,onUploadProgress:y,responseType:x,headers:v,withCredentials:N="same-origin",fetchOptions:S}=oh(n);x=x?(x+"").toLowerCase():"text";let H=Py([f,m&&m.toAbortSignal()],h),b;const E=H&&H.unsubscribe&&(()=>{H.unsubscribe()});let T;try{if(y&&l4&&o!=="get"&&o!=="head"&&(T=await n4(v,u))!==0){let Q=new Request(c,{method:"POST",body:u,duplex:"half"}),Z;if(B.isFormData(u)&&(Z=Q.headers.get("content-type"))&&v.setContentType(Z),Q.body){const[le,xe]=b0(T,Ys(j0(y)));u=S0(Q.body,E0,le,xe)}}B.isString(N)||(N=N?"include":"omit");const z="credentials"in Request.prototype;b=new Request(c,{...S,signal:H,method:o.toUpperCase(),headers:v.normalize().toJSON(),body:u,duplex:"half",credentials:z?N:void 0});let M=await fetch(b);const J=Lu&&(x==="stream"||x==="response");if(Lu&&(g||J&&E)){const Q={};["status","statusText","headers"].forEach(oe=>{Q[oe]=M[oe]});const Z=B.toFiniteNumber(M.headers.get("content-length")),[le,xe]=g&&b0(Z,Ys(j0(g),!0))||[];M=new Response(S0(M.body,E0,le,()=>{xe&&xe(),E&&E()}),Q)}x=x||"text";let k=await Vs[B.findKey(Vs,x)||"text"](M,n);return!J&&E&&E(),await new Promise((Q,Z)=>{ch(Q,Z,{data:k,headers:dt.from(M.headers),status:M.status,statusText:M.statusText,config:n,request:b})})}catch(z){throw E&&E(),z&&z.name==="TypeError"&&/Load failed|fetch/i.test(z.message)?Object.assign(new de("Network Error",de.ERR_NETWORK,n,b),{cause:z.cause||z}):de.from(z,z&&z.code,n,b)}}),Hu={http:by,xhr:$y,fetch:i4};B.forEach(Hu,(n,c)=>{if(n){try{Object.defineProperty(n,"name",{value:c})}catch{}Object.defineProperty(n,"adapterName",{value:c})}});const w0=n=>`- ${n}`,s4=n=>B.isFunction(n)||n===null||n===!1,mh={getAdapter:n=>{n=B.isArray(n)?n:[n];const{length:c}=n;let o,u;const f={};for(let m=0;m<c;m++){o=n[m];let h;if(u=o,!s4(o)&&(u=Hu[(h=String(o)).toLowerCase()],u===void 0))throw new de(`Unknown adapter '${h}'`);if(u)break;f[h||"#"+m]=u}if(!u){const m=Object.entries(f).map(([g,y])=>`adapter ${g} `+(y===!1?"is not supported by the environment":"is not available in the build"));let h=c?m.length>1?`since :
`+m.map(w0).join(`
`):" "+w0(m[0]):"as no adapter specified";throw new de("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return u},adapters:Hu};function Au(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new nn(null,n)}function A0(n){return Au(n),n.headers=dt.from(n.headers),n.data=wu.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),mh.getAdapter(n.adapter||hi.adapter)(n).then(function(u){return Au(n),u.data=wu.call(n,n.transformResponse,u),u.headers=dt.from(u.headers),u},function(u){return rh(u)||(Au(n),u&&u.response&&(u.response.data=wu.call(n,n.transformResponse,u.response),u.response.headers=dt.from(u.response.headers))),Promise.reject(u)})}const hh="1.9.0",Ws={};["object","boolean","number","function","string","symbol"].forEach((n,c)=>{Ws[n]=function(u){return typeof u===n||"a"+(c<1?"n ":" ")+n}});const T0={};Ws.transitional=function(c,o,u){function f(m,h){return"[Axios v"+hh+"] Transitional option '"+m+"'"+h+(u?". "+u:"")}return(m,h,g)=>{if(c===!1)throw new de(f(h," has been removed"+(o?" in "+o:"")),de.ERR_DEPRECATED);return o&&!T0[h]&&(T0[h]=!0,console.warn(f(h," has been deprecated since v"+o+" and will be removed in the near future"))),c?c(m,h,g):!0}};Ws.spelling=function(c){return(o,u)=>(console.warn(`${u} is likely a misspelling of ${c}`),!0)};function r4(n,c,o){if(typeof n!="object")throw new de("options must be an object",de.ERR_BAD_OPTION_VALUE);const u=Object.keys(n);let f=u.length;for(;f-- >0;){const m=u[f],h=c[m];if(h){const g=n[m],y=g===void 0||h(g,m,n);if(y!==!0)throw new de("option "+m+" must be "+y,de.ERR_BAD_OPTION_VALUE);continue}if(o!==!0)throw new de("Unknown option "+m,de.ERR_BAD_OPTION)}}const Hs={assertOptions:r4,validators:Ws},Ft=Hs.validators;let oa=class{constructor(c){this.defaults=c||{},this.interceptors={request:new g0,response:new g0}}async request(c,o){try{return await this._request(c,o)}catch(u){if(u instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const m=f.stack?f.stack.replace(/^.+\n/,""):"";try{u.stack?m&&!String(u.stack).endsWith(m.replace(/^.+\n.+\n/,""))&&(u.stack+=`
`+m):u.stack=m}catch{}}throw u}}_request(c,o){typeof c=="string"?(o=o||{},o.url=c):o=c||{},o=fa(this.defaults,o);const{transitional:u,paramsSerializer:f,headers:m}=o;u!==void 0&&Hs.assertOptions(u,{silentJSONParsing:Ft.transitional(Ft.boolean),forcedJSONParsing:Ft.transitional(Ft.boolean),clarifyTimeoutError:Ft.transitional(Ft.boolean)},!1),f!=null&&(B.isFunction(f)?o.paramsSerializer={serialize:f}:Hs.assertOptions(f,{encode:Ft.function,serialize:Ft.function},!0)),o.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?o.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:o.allowAbsoluteUrls=!0),Hs.assertOptions(o,{baseUrl:Ft.spelling("baseURL"),withXsrfToken:Ft.spelling("withXSRFToken")},!0),o.method=(o.method||this.defaults.method||"get").toLowerCase();let h=m&&B.merge(m.common,m[o.method]);m&&B.forEach(["delete","get","head","post","put","patch","common"],b=>{delete m[b]}),o.headers=dt.concat(h,m);const g=[];let y=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(o)===!1||(y=y&&E.synchronous,g.unshift(E.fulfilled,E.rejected))});const x=[];this.interceptors.response.forEach(function(E){x.push(E.fulfilled,E.rejected)});let v,N=0,S;if(!y){const b=[A0.bind(this),void 0];for(b.unshift.apply(b,g),b.push.apply(b,x),S=b.length,v=Promise.resolve(o);N<S;)v=v.then(b[N++],b[N++]);return v}S=g.length;let H=o;for(N=0;N<S;){const b=g[N++],E=g[N++];try{H=b(H)}catch(T){E.call(this,T);break}}try{v=A0.call(this,H)}catch(b){return Promise.reject(b)}for(N=0,S=x.length;N<S;)v=v.then(x[N++],x[N++]);return v}getUri(c){c=fa(this.defaults,c);const o=uh(c.baseURL,c.url,c.allowAbsoluteUrls);return nh(o,c.params,c.paramsSerializer)}};B.forEach(["delete","get","head","options"],function(c){oa.prototype[c]=function(o,u){return this.request(fa(u||{},{method:c,url:o,data:(u||{}).data}))}});B.forEach(["post","put","patch"],function(c){function o(u){return function(m,h,g){return this.request(fa(g||{},{method:c,headers:u?{"Content-Type":"multipart/form-data"}:{},url:m,data:h}))}}oa.prototype[c]=o(),oa.prototype[c+"Form"]=o(!0)});let c4=class ph{constructor(c){if(typeof c!="function")throw new TypeError("executor must be a function.");let o;this.promise=new Promise(function(m){o=m});const u=this;this.promise.then(f=>{if(!u._listeners)return;let m=u._listeners.length;for(;m-- >0;)u._listeners[m](f);u._listeners=null}),this.promise.then=f=>{let m;const h=new Promise(g=>{u.subscribe(g),m=g}).then(f);return h.cancel=function(){u.unsubscribe(m)},h},c(function(m,h,g){u.reason||(u.reason=new nn(m,h,g),o(u.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(c){if(this.reason){c(this.reason);return}this._listeners?this._listeners.push(c):this._listeners=[c]}unsubscribe(c){if(!this._listeners)return;const o=this._listeners.indexOf(c);o!==-1&&this._listeners.splice(o,1)}toAbortSignal(){const c=new AbortController,o=u=>{c.abort(u)};return this.subscribe(o),c.signal.unsubscribe=()=>this.unsubscribe(o),c.signal}static source(){let c;return{token:new ph(function(f){c=f}),cancel:c}}};function u4(n){return function(o){return n.apply(null,o)}}function o4(n){return B.isObject(n)&&n.isAxiosError===!0}const Bu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Bu).forEach(([n,c])=>{Bu[c]=n});function xh(n){const c=new oa(n),o=k0(oa.prototype.request,c);return B.extend(o,oa.prototype,c,{allOwnKeys:!0}),B.extend(o,c,null,{allOwnKeys:!0}),o.create=function(f){return xh(fa(n,f))},o}const Ge=xh(hi);Ge.Axios=oa;Ge.CanceledError=nn;Ge.CancelToken=c4;Ge.isCancel=rh;Ge.VERSION=hh;Ge.toFormData=$s;Ge.AxiosError=de;Ge.Cancel=Ge.CanceledError;Ge.all=function(c){return Promise.all(c)};Ge.spread=u4;Ge.isAxiosError=o4;Ge.mergeConfig=fa;Ge.AxiosHeaders=dt;Ge.formToJSON=n=>sh(B.isHTMLForm(n)?new FormData(n):n);Ge.getAdapter=mh.getAdapter;Ge.HttpStatusCode=Bu;Ge.default=Ge;const{Axios:tg,AxiosError:lg,CanceledError:ag,isCancel:ng,CancelToken:ig,VERSION:sg,all:rg,Cancel:cg,isAxiosError:ug,spread:og,toFormData:fg,AxiosHeaders:dg,HttpStatusCode:mg,formToJSON:hg,getAdapter:pg,mergeConfig:xg}=Ge,f4="http://localhost:5000/api",De=Ge.create({baseURL:f4,headers:{"Content-Type":"application/json"}});De.interceptors.request.use(n=>{const c=localStorage.getItem("userInfo");if(c){const{token:o}=JSON.parse(c);o&&(n.headers.Authorization=`Bearer ${o}`)}return n},n=>Promise.reject(n));De.interceptors.response.use(n=>n,n=>{var c;return((c=n.response)==null?void 0:c.status)===401&&(localStorage.removeItem("userInfo"),window.location.href="/login"),Promise.reject(n)});const R0={login:n=>De.post("/auth/login",n),register:n=>De.post("/auth/register",n),getProfile:()=>De.get("/auth/profile"),updateProfile:n=>De.put("/auth/profile",n)},Gl={getJobs:n=>De.get("/jobs",{params:n}),getJobById:n=>De.get(`/jobs/${n}`),createJob:n=>De.post("/jobs",n),updateJob:(n,c)=>De.put(`/jobs/${n}`,c),deleteJob:n=>De.delete(`/jobs/${n}`),getEmployerJobs:()=>De.get("/jobs/employer"),incrementJobView:n=>De.put(`/jobs/${n}/view`)},Gs={getMyProfile:()=>De.get("/profiles/me"),updateMyProfile:n=>De.put("/profiles/me",n),getProfileById:n=>De.get(`/profiles/${n}`),searchProfiles:n=>De.get("/profiles/search",{params:n})},da={applyForJob:(n,c)=>De.post(`/applications/job/${n}`,c),getMyApplications:()=>De.get("/applications/candidate/me"),getJobApplications:n=>De.get(`/applications/job/${n}`),updateApplicationStatus:(n,c)=>De.patch(`/applications/${n}/status`,{status:c}),getApplicationById:n=>De.get(`/applications/${n}`),withdrawApplication:n=>De.delete(`/applications/${n}`)},d4={getEmployerAnalytics:()=>De.get("/analytics/employer"),getGeneralAnalytics:()=>De.get("/analytics/general"),getJobPostingsAnalytics:()=>De.get("/analytics/employer/jobs")},yh=R.createContext(),wt=()=>R.useContext(yh),m4=({children:n})=>{const[c,o]=R.useState(null),[u,f]=R.useState(!0),[m,h]=R.useState(null);R.useEffect(()=>{const N=localStorage.getItem("userInfo");if(N)try{const S=JSON.parse(N);S.token&&(_x(S.token).exp*1e3<Date.now()?(localStorage.removeItem("userInfo"),o(null)):o(S))}catch(S){console.error("Invalid token:",S),localStorage.removeItem("userInfo"),o(null)}f(!1)},[]);const v={user:c,loading:u,error:m,login:async(N,S)=>{var H,b;f(!0),h(null);try{const T=(await R0.login({email:N,password:S})).data;return localStorage.setItem("userInfo",JSON.stringify(T)),o(T),T}catch(E){throw h(((b=(H=E.response)==null?void 0:H.data)==null?void 0:b.message)||"Login failed"),E}finally{f(!1)}},register:async N=>{var S,H;f(!0),h(null);try{const E=(await R0.register(N)).data;return localStorage.setItem("userInfo",JSON.stringify(E)),o(E),E}catch(b){throw h(((H=(S=b.response)==null?void 0:S.data)==null?void 0:H.message)||"Registration failed"),b}finally{f(!1)}},logout:()=>{localStorage.removeItem("userInfo"),o(null)},setUser:o,setError:h};return s.jsx(yh.Provider,{value:v,children:!u&&n})};var gh={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},C0=Yl.createContext&&Yl.createContext(gh),Vl=function(){return Vl=Object.assign||function(n){for(var c,o=1,u=arguments.length;o<u;o++){c=arguments[o];for(var f in c)Object.prototype.hasOwnProperty.call(c,f)&&(n[f]=c[f])}return n},Vl.apply(this,arguments)},h4=function(n,c){var o={};for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&c.indexOf(u)<0&&(o[u]=n[u]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,u=Object.getOwnPropertySymbols(n);f<u.length;f++)c.indexOf(u[f])<0&&Object.prototype.propertyIsEnumerable.call(n,u[f])&&(o[u[f]]=n[u[f]]);return o};function vh(n){return n&&n.map(function(c,o){return Yl.createElement(c.tag,Vl({key:o},c.attr),vh(c.child))})}function ue(n){return function(c){return Yl.createElement(p4,Vl({attr:Vl({},n.attr)},c),vh(n.child))}}function p4(n){var c=function(o){var u=n.attr,f=n.size,m=n.title,h=h4(n,["attr","size","title"]),g=f||o.size||"1em",y;return o.className&&(y=o.className),n.className&&(y=(y?y+" ":"")+n.className),Yl.createElement("svg",Vl({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},o.attr,u,h,{className:y,style:Vl(Vl({color:n.color||o.color},o.style),n.style),height:g,width:g,xmlns:"http://www.w3.org/2000/svg"}),m&&Yl.createElement("title",null,m),n.children)};return C0!==void 0?Yl.createElement(C0.Consumer,null,function(o){return c(o)}):c(gh)}function bh(n){return ue({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"}}]})(n)}function jh(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"}}]})(n)}function x4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"}}]})(n)}function qu(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M257.5 445.1l-22.2 22.2c-9.4 9.4-24.6 9.4-33.9 0L7 273c-9.4-9.4-9.4-24.6 0-33.9L201.4 44.7c9.4-9.4 24.6-9.4 33.9 0l22.2 22.2c9.5 9.5 9.3 25-.4 34.3L136.6 216H424c13.3 0 24 10.7 24 24v32c0 13.3-10.7 24-24 24H136.6l120.5 114.8c9.8 9.3 10 24.8.4 34.3z"}}]})(n)}function Ut(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M320 336c0 8.84-7.16 16-16 16h-96c-8.84 0-16-7.16-16-16v-48H0v144c0 25.6 22.4 48 48 48h416c25.6 0 48-22.4 48-48V288H320v48zm144-208h-80V80c0-25.6-22.4-48-48-48H176c-25.6 0-48 22.4-48 48v48H48c-25.6 0-48 22.4-48 48v80h512v-80c0-25.6-22.4-48-48-48zm-144 0H192V96h128v32z"}}]})(n)}function pi(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M436 480h-20V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v456H12c-6.627 0-12 5.373-12 12v20h448v-20c0-6.627-5.373-12-12-12zM128 76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76zm0 96c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40zm52 148h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12zm76 160h-64v-84c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v84zm64-172c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40z"}}]})(n)}function to(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"}}]})(n)}function y4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M332.8 320h38.4c6.4 0 12.8-6.4 12.8-12.8V172.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V76.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-288 0h38.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zM496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z"}}]})(n)}function St(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"}}]})(n)}function g4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"}}]})(n)}function Nh(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"}}]})(n)}function Sh(n){return ue({attr:{viewBox:"0 0 288 512"},child:[{tag:"path",attr:{d:"M209.2 233.4l-108-31.6C88.7 198.2 80 186.5 80 173.5c0-16.3 13.2-29.5 29.5-29.5h66.3c12.2 0 24.2 3.7 34.2 10.5 6.1 4.1 14.3 3.1 19.5-2l34.8-34c7.1-6.9 6.1-18.4-1.8-24.5C238 74.8 207.4 64.1 176 64V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48h-2.5C45.8 64-5.4 118.7.5 183.6c4.2 46.1 39.4 83.6 83.8 96.6l102.5 30c12.5 3.7 21.2 15.3 21.2 28.3 0 16.3-13.2 29.5-29.5 29.5h-66.3C100 368 88 364.3 78 357.5c-6.1-4.1-14.3-3.1-19.5 2l-34.8 34c-7.1 6.9-6.1 18.4 1.8 24.5 24.5 19.2 55.1 29.9 86.5 30v48c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-48.2c46.6-.9 90.3-28.6 105.7-72.7 21.5-61.6-14.6-124.8-72.5-141.7z"}}]})(n)}function v4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"}}]})(n)}function b4(n){return ue({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M402.6 83.2l90.2 90.2c3.8 3.8 3.8 10 0 13.8L274.4 405.6l-92.8 10.3c-12.4 1.4-22.9-9.1-21.5-21.5l10.3-92.8L388.8 83.2c3.8-3.8 10-3.8 13.8 0zm162-22.9l-48.8-48.8c-15.2-15.2-39.9-15.2-55.2 0l-35.4 35.4c-3.8 3.8-3.8 10 0 13.8l90.2 90.2c3.8 3.8 10 3.8 13.8 0l35.4-35.4c15.2-15.3 15.2-40 0-55.2zM384 346.2V448H64V128h229.8c3.2 0 6.2-1.3 8.5-3.5l40-40c7.6-7.6 2.2-20.5-8.5-20.5H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V306.2c0-10.7-12.9-16-20.5-8.5l-40 40c-2.2 2.3-3.5 5.3-3.5 8.5z"}}]})(n)}function Is(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z"}}]})(n)}function Eh(n){return ue({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"}}]})(n)}function wh(n){return ue({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"}}]})(n)}function er(n){return ue({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 236c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-64c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-72v8c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12zm96-114.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"}}]})(n)}function j4(n){return ue({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm76.45 211.36l-96.42 95.7c-6.65 6.61-17.39 6.61-24.04 0l-96.42-95.7C73.42 337.29 80.54 320 94.82 320H160v-80c0-8.84 7.16-16 16-16h32c8.84 0 16 7.16 16 16v80h65.18c14.28 0 21.4 17.29 11.27 27.36zM377 105L279.1 7c-4.5-4.5-10.6-7-17-7H256v128h128v-6.1c0-6.3-2.5-12.4-7-16.9z"}}]})(n)}function Ah(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.976 0H24.028C2.71 0-8.047 25.866 7.058 40.971L192 225.941V432c0 7.831 3.821 15.17 10.237 19.662l80 55.98C298.02 518.69 320 507.493 320 487.98V225.941l184.947-184.97C520.021 25.896 509.338 0 487.976 0z"}}]})(n)}function N4(n){return ue({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M336.5 160C322 70.7 287.8 8 248 8s-74 62.7-88.5 152h177zM152 256c0 22.2 1.2 43.5 3.3 64h185.3c2.1-20.5 3.3-41.8 3.3-64s-1.2-43.5-3.3-64H155.3c-2.1 20.5-3.3 41.8-3.3 64zm324.7-96c-28.6-67.9-86.5-120.4-158-141.6 24.4 33.8 41.2 84.7 50 141.6h108zM177.2 18.4C105.8 39.6 47.8 92.1 19.3 160h108c8.7-56.9 25.5-107.8 49.9-141.6zM487.4 192H372.7c2.1 21 3.3 42.5 3.3 64s-1.2 43-3.3 64h114.6c5.5-20.5 8.6-41.8 8.6-64s-3.1-43.5-8.5-64zM120 256c0-21.5 1.2-43 3.3-64H8.6C3.2 212.5 0 233.8 0 256s3.2 43.5 8.6 64h114.6c-2-21-3.2-42.5-3.2-64zm39.5 96c14.5 89.3 48.7 152 88.5 152s74-62.7 88.5-152h-177zm159.3 141.6c71.4-21.2 129.4-73.7 158-141.6h-108c-8.8 56.9-25.6 107.8-50 141.6zM19.3 352c28.6 67.9 86.5 120.4 158 141.6-24.4-33.8-41.2-84.7-50-141.6h-108z"}}]})(n)}function S4(n){return ue({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M622.34 153.2L343.4 67.5c-15.2-4.67-31.6-4.67-46.79 0L17.66 153.2c-23.54 7.23-23.54 38.36 0 45.59l48.63 14.94c-10.67 13.19-17.23 29.28-17.88 46.9C38.78 266.15 32 276.11 32 288c0 10.78 5.68 19.85 13.86 25.65L20.33 428.53C18.11 438.52 25.71 448 35.94 448h56.11c10.24 0 17.84-9.48 15.62-19.47L82.14 313.65C90.32 307.85 96 298.78 96 288c0-11.57-6.47-21.25-15.66-26.87.76-15.02 8.44-28.3 20.69-36.72L296.6 284.5c9.06 2.78 26.44 6.25 46.79 0l278.95-85.7c23.55-7.24 23.55-38.36 0-45.6zM352.79 315.09c-28.53 8.76-52.84 3.92-65.59 0l-145.02-44.55L128 384c0 35.35 85.96 64 192 64s192-28.65 192-64l-14.18-113.47-145.03 44.56z"}}]})(n)}function E4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z"}}]})(n)}function w4(n){return ue({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M280.37 148.26L96 300.11V464a16 16 0 0 0 16 16l112.06-.29a16 16 0 0 0 15.92-16V368a16 16 0 0 1 16-16h64a16 16 0 0 1 16 16v95.64a16 16 0 0 0 16 16.05L464 480a16 16 0 0 0 16-16V300L295.67 148.26a12.19 12.19 0 0 0-15.3 0zM571.6 251.47L488 182.56V44.05a12 12 0 0 0-12-12h-56a12 12 0 0 0-12 12v72.61L318.47 43a48 48 0 0 0-61 0L4.34 251.47a12 12 0 0 0-1.6 16.9l25.5 31A12 12 0 0 0 45.15 301l235.22-193.74a12.19 12.19 0 0 1 15.3 0L530.9 301a12 12 0 0 0 16.9-1.6l25.5-31a12 12 0 0 0-1.7-16.93z"}}]})(n)}function ma(n){return ue({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M360 0H24C10.745 0 0 10.745 0 24v16c0 13.255 10.745 24 24 24 0 90.965 51.016 167.734 120.842 192C75.016 280.266 24 357.035 24 448c-13.255 0-24 10.745-24 24v16c0 13.255 10.745 24 24 24h336c13.255 0 24-10.745 24-24v-16c0-13.255-10.745-24-24-24 0-90.965-51.016-167.734-120.842-192C308.984 231.734 360 154.965 360 64c13.255 0 24-10.745 24-24V24c0-13.255-10.745-24-24-24zm-75.078 384H99.08c17.059-46.797 52.096-80 92.92-80 40.821 0 75.862 33.196 92.922 80zm.019-256H99.078C91.988 108.548 88 86.748 88 64h208c0 22.805-3.987 44.587-11.059 64z"}}]})(n)}function A4(n){return ue({attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M96.06 454.35c.01 6.29 1.87 12.45 5.36 17.69l17.09 25.69a31.99 31.99 0 0 0 26.64 14.28h61.71a31.99 31.99 0 0 0 26.64-14.28l17.09-25.69a31.989 31.989 0 0 0 5.36-17.69l.04-38.35H96.01l.05 38.35zM0 176c0 44.37 16.45 84.85 43.56 115.78 16.52 18.85 42.36 58.23 52.21 91.45.04.26.07.52.11.78h160.24c.04-.26.07-.51.11-.78 9.85-33.22 35.69-72.6 52.21-91.45C335.55 260.85 352 220.37 352 176 352 78.61 272.91-.3 175.45 0 73.44.31 0 82.97 0 176zm176-80c-44.11 0-80 35.89-80 80 0 8.84-7.16 16-16 16s-16-7.16-16-16c0-61.76 50.24-112 112-112 8.84 0 16 7.16 16 16s-7.16 16-16 16z"}}]})(n)}function Yu(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 224h-24v-72C376 68.2 307.8 0 224 0S72 68.2 72 152v72H48c-26.5 0-48 21.5-48 48v192c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V272c0-26.5-21.5-48-48-48zm-104 0H152v-72c0-39.7 32.3-72 72-72s72 32.3 72 72v72z"}}]})(n)}function xi(n){return ue({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"}}]})(n)}function Vu(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z"}}]})(n)}function Th(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"}}]})(n)}function Gu(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm144 276c0 6.6-5.4 12-12 12h-92v92c0 6.6-5.4 12-12 12h-56c-6.6 0-12-5.4-12-12v-92h-92c-6.6 0-12-5.4-12-12v-56c0-6.6 5.4-12 12-12h92v-92c0-6.6 5.4-12 12-12h56c6.6 0 12 5.4 12 12v92h92c6.6 0 12 5.4 12 12v56z"}}]})(n)}function oi(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"}}]})(n)}function Rh(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M416 448h-84c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h84c17.7 0 32-14.3 32-32V160c0-17.7-14.3-32-32-32h-84c-6.6 0-12-5.4-12-12V76c0-6.6 5.4-12 12-12h84c53 0 96 43 96 96v192c0 53-43 96-96 96zm-47-201L201 79c-15-15-41-4.5-41 17v96H24c-13.3 0-24 10.7-24 24v96c0 13.3 10.7 24 24 24h136v96c0 21.5 26 32 41 17l168-168c9.3-9.4 9.3-24.6 0-34z"}}]})(n)}function T4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M497 273L329 441c-15 15-41 4.5-41-17v-96H152c-13.3 0-24-10.7-24-24v-96c0-13.3 10.7-24 24-24h136V88c0-21.4 25.9-32 41-17l168 168c9.3 9.4 9.3 24.6 0 34zM192 436v-40c0-6.6-5.4-12-12-12H96c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32h84c6.6 0 12-5.4 12-12V76c0-6.6-5.4-12-12-12H96c-53 0-96 43-96 96v192c0 53 43 96 96 96h84c6.6 0 12-5.4 12-12z"}}]})(n)}function ha(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"}}]})(n)}function z0(n){return ue({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M288 32C128.94 32 0 160.94 0 320c0 52.8 14.25 102.26 39.06 144.8 5.61 9.62 16.3 15.2 27.44 15.2h443c11.14 0 21.83-5.58 27.44-15.2C561.75 422.26 576 372.8 576 320c0-159.06-128.94-288-288-288zm0 64c14.71 0 26.58 10.13 30.32 23.65-1.11 2.26-2.64 4.23-3.45 6.67l-9.22 27.67c-5.13 3.49-10.97 6.01-17.64 6.01-17.67 0-32-14.33-32-32S270.33 96 288 96zM96 384c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32zm48-160c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32zm246.77-72.41l-61.33 184C343.13 347.33 352 364.54 352 384c0 11.72-3.38 22.55-8.88 32H232.88c-5.5-9.45-8.88-20.28-8.88-32 0-33.94 26.5-61.43 59.9-63.59l61.34-184.01c4.17-12.56 17.73-19.45 30.36-15.17 12.57 4.19 19.35 17.79 15.17 30.36zm14.66 57.2l15.52-46.55c3.47-1.29 7.13-2.23 11.05-2.23 17.67 0 32 14.33 32 32s-14.33 32-32 32c-11.38-.01-20.89-6.28-26.57-15.22zM480 384c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32z"}}]})(n)}function R4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M139.61 35.5a12 12 0 0 0-17 0L58.93 98.81l-22.7-22.12a12 12 0 0 0-17 0L3.53 92.41a12 12 0 0 0 0 17l47.59 47.4a12.78 12.78 0 0 0 17.61 0l15.59-15.62L156.52 69a12.09 12.09 0 0 0 .09-17zm0 159.19a12 12 0 0 0-17 0l-63.68 63.72-22.7-22.1a12 12 0 0 0-17 0L3.53 252a12 12 0 0 0 0 17L51 316.5a12.77 12.77 0 0 0 17.6 0l15.7-15.69 72.2-72.22a12 12 0 0 0 .09-16.9zM64 368c-26.49 0-48.59 21.5-48.59 48S37.53 464 64 464a48 48 0 0 0 0-96zm432 16H208a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h288a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-320H208a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h288a16 16 0 0 0 16-16V80a16 16 0 0 0-16-16zm0 160H208a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h288a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16z"}}]})(n)}function ml(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z"}}]})(n)}function Ch(n){return ue({attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"}}]})(n)}function C4(n){return ue({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M501.1 395.7L384 278.6c-23.1-23.1-57.6-27.6-85.4-13.9L192 158.1V96L64 0 0 64l96 128h62.1l106.6 106.6c-13.6 27.8-9.2 62.3 13.9 85.4l117.1 117.1c14.6 14.6 38.2 14.6 52.7 0l52.7-52.7c14.5-14.6 14.5-38.2 0-52.7zM331.7 225c28.3 0 54.9 11 74.9 31l19.4 19.4c15.8-6.9 30.8-16.5 43.8-29.5 37.1-37.1 49.7-89.3 37.9-136.7-2.2-9-13.5-12.1-20.1-5.5l-74.4 74.4-67.9-11.3L334 98.9l74.4-74.4c6.6-6.6 3.4-17.9-5.7-20.2-47.4-11.7-99.6.9-136.6 37.9-28.5 28.5-41.9 66.1-41.2 103.6l82.1 82.1c8.1-1.9 16.5-2.9 24.7-2.9zm-103.9 82l-56.7-56.7L18.7 402.8c-25 25-25 65.5 0 90.5s65.5 25 90.5 0l123.6-123.6c-7.6-19.9-9.9-41.6-5-62.7zM64 472c-13.2 0-24-10.8-24-24 0-13.3 10.7-24 24-24s24 10.7 24 24c0 13.2-10.7 24-24 24z"}}]})(n)}function z4(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"}}]})(n)}function zh(n){return ue({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm0 96c48.6 0 88 39.4 88 88s-39.4 88-88 88-88-39.4-88-88 39.4-88 88-88zm0 344c-58.7 0-111.3-26.6-146.5-68.2 18.8-35.4 55.6-59.8 98.5-59.8 2.4 0 4.8.4 7.1 1.1 13 4.2 26.6 6.9 40.9 6.9 14.3 0 28-2.7 40.9-6.9 2.3-.7 4.7-1.1 7.1-1.1 42.9 0 79.7 24.4 98.5 59.8C359.3 421.4 306.7 448 248 448z"}}]})(n)}function Xu(n){return ue({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h274.9c-2.4-6.8-3.4-14-2.6-21.3l6.8-60.9 1.2-11.1 7.9-7.9 77.3-77.3c-24.5-27.7-60-45.5-99.9-45.5zm45.3 145.3l-6.8 61c-1.1 10.2 7.5 18.8 17.6 17.6l60.9-6.8 137.9-137.9-71.7-71.7-137.9 137.8zM633 268.9L595.1 231c-9.3-9.3-24.5-9.3-33.8 0l-37.8 37.8-4.1 4.1 71.8 71.7 41.8-41.8c9.3-9.4 9.3-24.5 0-33.9z"}}]})(n)}function lo(n){return ue({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M624 208h-64v-64c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v64h-64c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h64v64c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-64h64c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-400 48c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(n)}function O4(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm95.8 32.6L272 480l-32-136 32-56h-96l32 56-32 136-47.8-191.4C56.9 292 0 350.3 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-72.1-56.9-130.4-128.2-133.8z"}}]})(n)}function D4(n){return ue({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(n)}function ao(n){return ue({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"}}]})(n)}const M4=()=>{const{user:n,logout:c}=wt(),o=Xl(),u=()=>{c(),o("/login")},f="text-primary border-b-2 border-primary",m="hover:text-primary transition-colors duration-200";return s.jsx("nav",{className:"bg-white shadow-md sticky top-0 z-50",children:s.jsxs("div",{className:"container mx-auto px-4 py-3 flex justify-between items-center",children:[s.jsxs(he,{to:"/",className:"text-2xl font-bold text-primary flex items-center",children:[s.jsx(Ut,{className:"mr-2"})," JobPortal"]}),s.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[s.jsx(fl,{to:"/",className:({isActive:h})=>h?f:m,end:!0,children:"Home"}),s.jsx(fl,{to:"/jobs",className:({isActive:h})=>h?f:m,children:"Find Jobs"})]}),s.jsx("div",{className:"flex items-center space-x-4",children:n?s.jsxs(s.Fragment,{children:[n.role==="employer"&&s.jsxs(fl,{to:"/employer/dashboard",className:({isActive:h})=>`flex items-center ${h?f:m}`,children:[s.jsx(z0,{className:"mr-1"})," Dashboard"]}),n.role==="candidate"&&s.jsxs(fl,{to:"/candidate/dashboard",className:({isActive:h})=>`flex items-center ${h?f:m}`,children:[s.jsx(z0,{className:"mr-1"})," Dashboard"]}),n.role==="employer"&&s.jsxs(fl,{to:"/employer/post-job",className:({isActive:h})=>`flex items-center ${h?f:m}`,children:[s.jsx(Gu,{className:"mr-1"})," Post Job"]}),n.role==="candidate"&&s.jsxs(fl,{to:"/candidate/profile",className:({isActive:h})=>`flex items-center ${h?f:m}`,children:[s.jsx(zh,{className:"mr-1"})," Profile"]}),s.jsxs("button",{onClick:u,className:"btn btn-primary py-2 px-3 text-sm flex items-center",children:[s.jsx(T4,{className:"mr-1"})," Logout"]})]}):s.jsxs(s.Fragment,{children:[s.jsxs(fl,{to:"/login",className:({isActive:h})=>`flex items-center ${h?f:m}`,children:[s.jsx(Rh,{className:"mr-1"})," Login"]}),s.jsxs(fl,{to:"/register",className:"btn btn-primary py-2 px-3 text-sm flex items-center",children:[s.jsx(lo,{className:"mr-1"})," Register"]})]})})]})})},_4=()=>{const n=new Date().getFullYear();return s.jsx("footer",{className:"bg-gray-800 text-gray-300 py-8 mt-auto",children:s.jsxs("div",{className:"container mx-auto px-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"JobPortal"}),s.jsx("p",{className:"text-sm",children:"Connecting talent with opportunity. Find your dream job or the perfect candidate with us."})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-3",children:"Quick Links"}),s.jsxs("ul",{className:"space-y-2 text-sm",children:[s.jsx("li",{children:s.jsx("a",{href:"/jobs",className:"hover:text-primary transition-colors",children:"Find Jobs"})}),s.jsx("li",{children:s.jsx("a",{href:"/employer/post-job",className:"hover:text-primary transition-colors",children:"Post a Job"})}),s.jsx("li",{children:s.jsx("a",{href:"/about",className:"hover:text-primary transition-colors",children:"About Us (Placeholder)"})}),s.jsx("li",{children:s.jsx("a",{href:"/contact",className:"hover:text-primary transition-colors",children:"Contact (Placeholder)"})})]})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-3",children:"Connect With Us"}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-primary transition-colors",children:s.jsx(bh,{size:24})}),s.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-primary transition-colors",children:s.jsx(jh,{size:24})}),s.jsx("a",{href:"#",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-primary transition-colors",children:s.jsx(x4,{size:24})})]})]})]}),s.jsxs("div",{className:"border-t border-gray-700 pt-6 text-center text-sm",children:[s.jsxs("p",{children:["© ",n," JobPortal. All rights reserved."]}),s.jsxs("p",{className:"mt-1",children:["Designed with ",s.jsx("span",{className:"text-red-500",children:"❤"})," by YourName/Team"]})]})]})})},U4=({job:n})=>s.jsxs("div",{className:"bg-white shadow-lg rounded-lg p-6 hover:shadow-xl transition-shadow duration-300",children:[s.jsxs("div",{className:"flex items-center mb-3",children:[s.jsx("div",{className:"bg-primary text-white rounded-full p-3 mr-4",children:s.jsx(Ut,{size:24})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-800 hover:text-primary transition-colors",children:s.jsx(he,{to:`/jobs/${n._id}`,children:n.title})}),s.jsx("p",{className:"text-gray-600 text-sm",children:n.companyName||"N/A"})]})]}),s.jsxs("div",{className:"text-sm text-gray-500 mb-1 flex items-center",children:[s.jsx(xi,{className:"mr-2 text-primary"})," ",n.location||"Remote"]}),s.jsxs("div",{className:"text-sm text-gray-500 mb-3 flex items-center",children:[s.jsx(pi,{className:"mr-2 text-primary"})," ",n.jobType||"Full-time"]}),s.jsx("p",{className:"text-gray-700 text-sm mb-4 truncate",children:n.description}),s.jsx(he,{to:`/jobs/${n._id}`,className:"btn btn-primary w-full text-center text-sm py-2",children:"View Details"})]}),L4=()=>{const[n,c]=R.useState(""),[o,u]=R.useState(""),[f,m]=R.useState([]),[h,g]=R.useState(!0),[y,x]=R.useState(null);R.useEffect(()=>{(async()=>{var S,H;try{g(!0);const b=await Gl.getJobs({pageSize:6,sortBy:"postedDate:desc"});m(b.data.jobs||[]),x(null)}catch(b){console.error("Error fetching recent jobs:",b),x(((H=(S=b.response)==null?void 0:S.data)==null?void 0:H.message)||"Failed to load recent jobs."),m([])}finally{g(!1)}})()},[]);const v=N=>{N.preventDefault();const S=new URLSearchParams;n&&S.append("search",n),o&&S.append("location",o),window.location.href=`/jobs?${S.toString()}`};return s.jsxs("div",{className:"space-y-12",children:[s.jsx("section",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20 px-6 rounded-lg shadow-xl",children:s.jsxs("div",{className:"container mx-auto text-center",children:[s.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Find Your Dream Job Today"}),s.jsx("p",{className:"text-lg md:text-xl mb-8",children:"Search thousands of job openings from top companies."}),s.jsxs("form",{onSubmit:v,className:"max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-md flex flex-col md:flex-row gap-4",children:[s.jsxs("div",{className:"flex-grow relative",children:[s.jsx(oi,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Job title, keywords, or company",value:n,onChange:N=>c(N.target.value),className:"w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-gray-700"})]}),s.jsxs("div",{className:"flex-grow relative",children:[s.jsx(xi,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Location (e.g., city, state)",value:o,onChange:N=>u(N.target.value),className:"w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-gray-700"})]}),s.jsx("button",{type:"submit",className:"btn btn-primary py-3 px-6 w-full md:w-auto",children:"Search Jobs"})]})]})}),s.jsxs("section",{children:[s.jsx("h2",{className:"text-3xl font-semibold mb-8 text-center text-gray-800",children:"Recent Job Postings"}),h&&s.jsx("p",{className:"text-center text-gray-600",children:"Loading jobs..."}),y&&s.jsx("p",{className:"text-center text-red-500",children:y}),!h&&!y&&f.length===0&&s.jsx("p",{className:"text-center text-gray-600",children:"No recent jobs found. Check back later!"}),!h&&!y&&f.length>0&&s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:f.map(N=>s.jsx(U4,{job:N},N._id))}),!h&&!y&&f.length>0&&s.jsx("div",{className:"text-center mt-10",children:s.jsx(he,{to:"/jobs",className:"btn btn-secondary text-lg px-8 py-3",children:"View All Jobs"})})]}),s.jsxs("section",{className:"bg-gray-100 py-16 px-6 rounded-lg",children:[s.jsx("h2",{className:"text-3xl font-semibold mb-10 text-center text-gray-800",children:"How It Works"}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-8 text-center",children:[s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"bg-primary text-white rounded-full p-4 inline-block mb-4",children:s.jsx(lo,{size:32})}),s.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-700",children:"1. Create Account"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Sign up as a candidate or employer to get started."})]}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"bg-primary text-white rounded-full p-4 inline-block mb-4",children:s.jsx(oi,{size:32})}),s.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-700",children:"2. Search or Post"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Candidates can search for jobs, employers can post openings."})]}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"bg-primary text-white rounded-full p-4 inline-block mb-4",children:s.jsx(Ut,{size:32})}),s.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-700",children:"3. Apply & Hire"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"Apply to jobs or review applications to find your match."})]})]})]}),s.jsxs("section",{className:"text-center py-12",children:[s.jsx("h2",{className:"text-2xl md:text-3xl font-semibold mb-4 text-gray-800",children:"Ready to Take the Next Step?"}),s.jsxs("div",{className:"space-x-0 md:space-x-4 space-y-4 md:space-y-0",children:[s.jsx(he,{to:"/register?role=candidate",className:"btn btn-primary text-lg px-8 py-3 inline-block w-full sm:w-auto",children:"Join as Candidate"}),s.jsx(he,{to:"/register?role=employer",className:"btn btn-secondary text-lg px-8 py-3 inline-block w-full sm:w-auto",children:"Join as Employer"})]})]})]})},H4=()=>{var S,H;const[n,c]=R.useState(""),[o,u]=R.useState(""),{login:f,error:m,setError:h,loading:g}=wt(),y=Xl(),v=((H=(S=Et().state)==null?void 0:S.from)==null?void 0:H.pathname)||"/",N=async b=>{b.preventDefault(),h(null);try{const E=await f(n,o);E.role==="employer"?y("/employer/dashboard"):E.role==="candidate"?y("/candidate/dashboard"):y(v,{replace:!0})}catch(E){console.error("Login failed:",E)}};return s.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-xl shadow-2xl",children:[s.jsxs("div",{children:[s.jsx(Rh,{className:"mx-auto h-12 w-auto text-primary"}),s.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),s.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",s.jsx(he,{to:"/register",className:"font-medium text-primary hover:text-indigo-500",children:"create a new account"})]})]}),s.jsxs("form",{className:"mt-8 space-y-6",onSubmit:N,children:[m&&s.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[s.jsx("strong",{className:"font-bold",children:"Error: "}),s.jsx("span",{className:"block sm:inline",children:m})]}),s.jsxs("div",{className:"rounded-md shadow-sm -space-y-px",children:[s.jsxs("div",{className:"relative mb-4",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Is,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"input-field pl-10",placeholder:"Email address",value:n,onChange:b=>c(b.target.value)})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Yu,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"input-field pl-10",placeholder:"Password",value:o,onChange:b=>u(b.target.value)})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary focus:ring-indigo-500 border-gray-300 rounded"}),s.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),s.jsx("div",{className:"text-sm",children:s.jsx(he,{to:"/forgot-password",className:"font-medium text-primary hover:text-indigo-500",children:"Forgot your password?"})})]}),s.jsx("div",{children:s.jsx("button",{type:"submit",disabled:g,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:g?"Signing in...":"Sign in"})})]})]})})},B4=()=>{const[n,c]=R.useState({name:"",email:"",password:"",confirmPassword:"",role:"candidate",companyName:""}),{register:o,error:u,setError:f,loading:m}=wt(),h=Xl(),g=Et();R.useEffect(()=>{const z=new URLSearchParams(g.search).get("role");z&&(z==="candidate"||z==="employer")&&c(M=>({...M,role:z}))},[g.search]);const{name:y,email:x,password:v,confirmPassword:N,role:S,companyName:H}=n,b=T=>{c({...n,[T.target.name]:T.target.value})},E=async T=>{if(T.preventDefault(),f(null),v!==N){f("Passwords do not match");return}if(S==="employer"&&!H.trim()){f("Company name is required for employers");return}try{const z={name:y,email:x,password:v,role:S};S==="employer"&&(z.companyName=H);const M=await o(z);M.role==="employer"?h("/employer/dashboard"):M.role==="candidate"?h("/candidate/dashboard"):h("/")}catch(z){console.error("Registration failed:",z)}};return s.jsx("div",{className:"min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-lg w-full space-y-8 bg-white p-10 rounded-xl shadow-2xl",children:[s.jsxs("div",{children:[s.jsx(lo,{className:"mx-auto h-12 w-auto text-primary"}),s.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),s.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Already have an account?"," ",s.jsx(he,{to:"/login",className:"font-medium text-primary hover:text-indigo-500",children:"Sign in"})]})]}),s.jsxs("form",{className:"mt-8 space-y-6",onSubmit:E,children:[u&&s.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[s.jsx("strong",{className:"font-bold",children:"Error: "}),s.jsx("span",{className:"block sm:inline",children:u})]}),s.jsxs("div",{className:"rounded-md shadow-sm space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(D4,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{name:"name",type:"text",required:!0,className:"input-field pl-10",placeholder:"Full Name",value:y,onChange:b})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Is,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{name:"email",type:"email",autoComplete:"email",required:!0,className:"input-field pl-10",placeholder:"Email address",value:x,onChange:b})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Yu,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{name:"password",type:"password",autoComplete:"new-password",required:!0,className:"input-field pl-10",placeholder:"Password",value:v,onChange:b})]}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Yu,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"input-field pl-10",placeholder:"Confirm Password",value:N,onChange:b})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"I am a:"}),s.jsxs("select",{name:"role",id:"role",value:S,onChange:b,className:"input-field appearance-none",children:[s.jsx("option",{value:"candidate",children:"Candidate (Looking for a job)"}),s.jsx("option",{value:"employer",children:"Employer (Looking to hire)"})]})]}),S==="employer"&&s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(pi,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{name:"companyName",type:"text",className:"input-field pl-10",placeholder:"Company Name",value:H,onChange:b,required:S==="employer"})]})]}),s.jsx("div",{children:s.jsx("button",{type:"submit",disabled:m,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:m?"Registering...":"Create Account"})})]})]})})},q4=({job:n})=>s.jsxs("div",{className:"bg-white shadow-lg rounded-lg p-6 hover:shadow-xl transition-shadow duration-300 flex flex-col justify-between",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-start mb-3",children:[s.jsx("div",{className:"bg-primary text-white rounded-lg p-3 mr-4 mt-1",children:s.jsx(Ut,{size:22})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-800 hover:text-primary transition-colors",children:s.jsx(he,{to:`/jobs/${n._id}`,children:n.title})}),s.jsx("p",{className:"text-gray-600 text-sm",children:n.companyName||"N/A"})]})]}),s.jsxs("div",{className:"text-sm text-gray-500 mb-1 flex items-center",children:[s.jsx(xi,{className:"mr-2 text-primary w-4"})," ",n.location||"Remote"]}),s.jsxs("div",{className:"text-sm text-gray-500 mb-1 flex items-center",children:[s.jsx(pi,{className:"mr-2 text-primary w-4"})," ",n.jobType||"Full-time"]}),n.salary&&s.jsxs("div",{className:"text-sm text-gray-500 mb-1 flex items-center",children:[s.jsx(Sh,{className:"mr-2 text-primary w-4"}),typeof n.salary=="number"?`$${n.salary.toLocaleString()}`:n.salary,n.salaryPeriod&&` per ${n.salaryPeriod}`]}),s.jsxs("div",{className:"text-sm text-gray-500 mb-3 flex items-center",children:[s.jsx(Nh,{className:"mr-2 text-primary w-4"})," Posted ",new Date(n.postedDate).toLocaleDateString()]}),s.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-3",children:n.description})]}),s.jsx(he,{to:`/jobs/${n._id}`,className:"btn btn-primary w-full text-center text-sm py-2 mt-auto",children:"View Details & Apply"})]}),Y4=()=>{const n=Et(),c=Xl(),[o,u]=R.useState([]),[f,m]=R.useState(!0),[h,g]=R.useState(null),[y,x]=R.useState(""),[v,N]=R.useState(""),[S,H]=R.useState(""),[b,E]=R.useState("-postedDate"),[T,z]=R.useState(1),[M,J]=R.useState(1),[k,Q]=R.useState(0),Z=9,le=R.useCallback(async()=>{var ee,ae;m(!0),g(null);try{const re=new URLSearchParams;y&&re.append("search",y),v&&re.append("location",v),S&&re.append("jobType",S),b&&re.append("sort",b),re.append("page",T),re.append("limit",Z),c(`${n.pathname}?${re.toString()}`,{replace:!0});const Me=await Gl.getJobs(Object.fromEntries(re));u(Me.data.jobs||[]),J(Me.data.totalPages||1),Q(Me.data.totalJobs||0)}catch(re){console.error("Error fetching jobs:",re),g(((ae=(ee=re.response)==null?void 0:ee.data)==null?void 0:ae.message)||"Failed to load jobs."),u([])}finally{m(!1)}},[y,v,S,b,T,c,n.pathname]);R.useEffect(()=>{const ee=new URLSearchParams(n.search);x(ee.get("search")||""),N(ee.get("location")||""),H(ee.get("jobType")||""),E(ee.get("sort")||"-postedDate"),z(parseInt(ee.get("page"))||1)},[n.search]),R.useEffect(()=>{le()},[le]);const xe=ee=>{ee.preventDefault(),z(1),le()},oe=()=>{x(""),N(""),H(""),E("-postedDate"),z(1)},ze=ee=>{ee>=1&&ee<=M&&z(ee)};return s.jsxs("div",{className:"container mx-auto py-8 px-4",children:[s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-8 text-center",children:"Find Your Next Opportunity"}),s.jsxs("form",{onSubmit:xe,className:"bg-white p-6 rounded-lg shadow-lg mb-8 space-y-4",children:[s.jsxs("div",{className:"grid md:grid-cols-3 gap-4 items-end",children:[s.jsxs("div",{className:"relative",children:[s.jsx("label",{htmlFor:"searchTerm",className:"block text-sm font-medium text-gray-700 mb-1",children:"Keywords"}),s.jsx(oi,{className:"absolute left-3 top-10 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",id:"searchTerm",placeholder:"Job title, company, skills",value:y,onChange:ee=>x(ee.target.value),className:"input-field pl-10 w-full"})]}),s.jsxs("div",{className:"relative",children:[s.jsx("label",{htmlFor:"searchLocation",className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),s.jsx(xi,{className:"absolute left-3 top-10 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",id:"searchLocation",placeholder:"City, state, or remote",value:v,onChange:ee=>N(ee.target.value),className:"input-field pl-10 w-full"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"jobType",className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Type"}),s.jsxs("select",{id:"jobType",value:S,onChange:ee=>H(ee.target.value),className:"input-field w-full",children:[s.jsx("option",{value:"",children:"All Types"}),s.jsx("option",{value:"Full-time",children:"Full-time"}),s.jsx("option",{value:"Part-time",children:"Part-time"}),s.jsx("option",{value:"Contract",children:"Contract"}),s.jsx("option",{value:"Internship",children:"Internship"}),s.jsx("option",{value:"Temporary",children:"Temporary"})]})]})]}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-4 items-end",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"sortBy",className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"}),s.jsxs("select",{id:"sortBy",value:b,onChange:ee=>E(ee.target.value),className:"input-field w-full",children:[s.jsx("option",{value:"-postedDate",children:"Newest"}),s.jsx("option",{value:"postedDate",children:"Oldest"}),s.jsx("option",{value:"title",children:"Title (A-Z)"}),s.jsx("option",{value:"-title",children:"Title (Z-A)"})]})]}),s.jsxs("div",{className:"md:col-span-2 flex flex-col sm:flex-row sm:items-end space-y-2 sm:space-y-0 sm:space-x-2 pt-5",children:[s.jsxs("button",{type:"submit",className:"btn btn-primary w-full sm:w-auto flex items-center justify-center",children:[s.jsx(Ah,{className:"mr-2"})," Apply Filters"]}),s.jsxs("button",{type:"button",onClick:oe,className:"btn bg-gray-200 text-gray-700 hover:bg-gray-300 w-full sm:w-auto flex items-center justify-center",children:[s.jsx(Ch,{className:"mr-2"})," Clear Filters"]})]})]})]}),f&&s.jsx("p",{className:"text-center text-gray-600 text-lg py-10",children:"Loading jobs..."}),h&&s.jsx("p",{className:"text-center text-red-500 bg-red-100 p-4 rounded-md",children:h}),!f&&!h&&o.length===0&&s.jsx("p",{className:"text-center text-gray-600 text-lg py-10 bg-gray-50 rounded-md",children:"No jobs found matching your criteria. Try broadening your search!"}),!f&&!h&&o.length>0&&s.jsxs(s.Fragment,{children:[s.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:["Showing ",o.length," of ",k," jobs."]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(ee=>s.jsx(q4,{job:ee},ee._id))})]}),!f&&!h&&M>1&&s.jsxs("div",{className:"mt-12 flex justify-center items-center space-x-2",children:[s.jsx("button",{onClick:()=>ze(T-1),disabled:T===1,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),[...Array(M).keys()].map(ee=>s.jsx("button",{onClick:()=>ze(ee+1),className:`px-4 py-2 border rounded-md text-sm font-medium ${T===ee+1?"bg-primary text-white border-primary":"border-gray-300 text-gray-700 bg-white hover:bg-gray-50"}`,children:ee+1},ee+1)),s.jsx("button",{onClick:()=>ze(T+1),disabled:T===M,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Next"})]})]})},V4=()=>{const{jobId:n}=Js(),{user:c,loading:o}=wt(),u=Xl(),f=Et(),[m,h]=R.useState(null),[g,y]=R.useState(!0),[x,v]=R.useState(null),[N,S]=R.useState({applied:!1,message:"",type:""}),[H,b]=R.useState(!1);R.useEffect(()=>{n&&(async()=>{var ae,re;y(!0),v(null);try{const Me=await Gl.getJobById(n);h(Me.data),Gl.incrementJobView(n).catch(W=>console.warn("Failed to increment view count",W))}catch(Me){console.error("Error fetching job details:",Me),v(((re=(ae=Me.response)==null?void 0:ae.data)==null?void 0:re.message)||"Failed to load job details.")}y(!1)})()},[n]),R.useEffect(()=>{(async()=>{if(c&&c.role==="candidate"&&m)try{const re=(await da.getMyApplications()).data.find(Me=>Me.jobId===n);re&&S({applied:!0,message:`You applied on ${new Date(re.applicationDate).toLocaleDateString()}`,type:"info"})}catch(ae){console.warn("Could not check existing application status",ae)}})()},[c,m,n]);const E=async()=>{var ee,ae;if(!c){u("/login",{state:{from:f}});return}if(c.role!=="candidate"){S({applied:!1,message:"Only candidates can apply for jobs.",type:"error"});return}b(!0),S({applied:!1,message:"",type:""});try{await da.applyForJob({jobId:n}),S({applied:!0,message:"Successfully applied for this job!",type:"success"})}catch(re){console.error("Error applying for job:",re),S({applied:!1,message:((ae=(ee=re.response)==null?void 0:ee.data)==null?void 0:ae.message)||"Failed to apply. You may have already applied or an error occurred.",type:"error"})}finally{b(!1)}};if(o||g)return s.jsx("div",{className:"text-center py-10",children:s.jsx("p",{className:"text-lg text-gray-600",children:"Loading job details..."})});if(x)return s.jsxs("div",{className:"text-center py-10",children:[s.jsx(ml,{className:"text-red-500 text-5xl mx-auto mb-4"}),s.jsxs("p",{className:"text-lg text-red-600 bg-red-100 p-4 rounded-md",children:["Error: ",x]}),s.jsxs(he,{to:"/jobs",className:"btn btn-primary mt-6 inline-flex items-center",children:[s.jsx(qu,{className:"mr-2"})," Back to Jobs"]})]});if(!m)return s.jsx("div",{className:"text-center py-10",children:s.jsx("p",{className:"text-lg text-gray-600",children:"Job not found."})});const{title:T,companyName:z,location:M,jobType:J,salary:k,salaryPeriod:Q,description:Z,requirements:le,skills:xe,postedDate:oe,employerId:ze}=m;return s.jsxs("div",{className:"container mx-auto py-8 px-4",children:[s.jsx("div",{className:"mb-6",children:s.jsxs(he,{to:"/jobs",className:"text-primary hover:underline flex items-center",children:[s.jsx(qu,{className:"mr-2"})," Back to all jobs"]})}),s.jsx("div",{className:"bg-white shadow-xl rounded-lg overflow-hidden",children:s.jsxs("div",{className:"p-6 md:p-8",children:[s.jsxs("div",{className:"md:flex justify-between items-start mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-2",children:T}),s.jsx("p",{className:"text-xl text-gray-600 mb-1",children:z}),s.jsxs("div",{className:"flex items-center text-gray-500 text-sm mb-4",children:[s.jsx(xi,{className:"mr-2 text-primary"})," ",M||"Remote",s.jsx("span",{className:"mx-2",children:"|"}),s.jsx(Ut,{className:"mr-2 text-primary"})," ",J||"Full-time",s.jsx("span",{className:"mx-2",children:"|"}),s.jsx(Nh,{className:"mr-2 text-primary"})," Posted: ",new Date(oe).toLocaleDateString()]})]}),c&&c.role==="candidate"&&!N.applied&&s.jsxs("button",{onClick:E,disabled:H,className:"btn btn-primary py-3 px-6 text-lg w-full md:w-auto mt-4 md:mt-0 flex items-center justify-center disabled:opacity-70",children:[s.jsx(Vu,{className:"mr-2"})," ",H?"Applying...":"Apply Now"]}),c&&c.role==="candidate"&&N.applied&&N.type==="success"&&s.jsxs("div",{className:"mt-4 md:mt-0 p-3 rounded-md bg-green-100 text-green-700 flex items-center",children:[s.jsx(St,{className:"mr-2"})," ",N.message]}),c&&c.role==="candidate"&&N.applied&&N.type==="info"&&s.jsxs("div",{className:"mt-4 md:mt-0 p-3 rounded-md bg-blue-100 text-blue-700 flex items-center",children:[s.jsx(St,{className:"mr-2"})," ",N.message]})]}),N.message&&N.type==="error"&&s.jsxs("div",{className:"mb-6 p-3 rounded-md bg-red-100 text-red-700 flex items-center",children:[s.jsx(ml,{className:"mr-2"})," ",N.message]}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-6 mb-8",children:[k&&s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx(Sh,{className:"text-primary text-2xl mb-2"}),s.jsx("h3",{className:"font-semibold text-gray-700",children:"Salary"}),s.jsxs("p",{className:"text-gray-600",children:[typeof k=="number"?`$${k.toLocaleString()}`:k,Q&&` per ${Q}`]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx(pi,{className:"text-primary text-2xl mb-2"}),s.jsx("h3",{className:"font-semibold text-gray-700",children:"Company"}),s.jsx("p",{className:"text-gray-600",children:z})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx(ao,{className:"text-primary text-2xl mb-2"}),s.jsx("h3",{className:"font-semibold text-gray-700",children:"Employer"}),s.jsxs("p",{className:"text-gray-600",children:["Details about employer (ID: ",ze.slice(0,10),"...)"]})]})]}),s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-semibold text-gray-800 mb-3 flex items-center",children:[s.jsx(R4,{className:"mr-3 text-primary"}),"Job Description"]}),s.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:(Z==null?void 0:Z.replace(/\n/g,"<br />"))||"No description provided."}})]}),le&&le.length>0&&s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-semibold text-gray-800 mb-3 flex items-center",children:[s.jsx(A4,{className:"mr-3 text-primary"}),"Requirements"]}),s.jsx("ul",{className:"list-disc list-inside pl-5 text-gray-700 space-y-1",children:le.map((ee,ae)=>s.jsx("li",{children:ee},ae))})]}),xe&&xe.length>0&&s.jsxs("div",{className:"mb-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-3",children:"Skills Required"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:xe.map((ee,ae)=>s.jsx("span",{className:"bg-secondary text-gray-800 px-3 py-1 rounded-full text-sm font-medium",children:ee},ae))})]}),c&&c.role==="candidate"&&!N.applied&&s.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:s.jsxs("button",{onClick:E,disabled:H,className:"btn btn-primary py-3 px-8 text-lg w-full md:w-auto flex items-center justify-center disabled:opacity-70",children:[s.jsx(Vu,{className:"mr-2"})," ",H?"Submitting Application...":"Apply for this Job"]})}),c&&c.role==="candidate"&&N.applied&&s.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[s.jsxs("p",{className:`p-3 rounded-md ${N.type==="success"||N.type==="info"?"bg-green-100 text-green-700":"bg-red-100 text-red-700"} flex items-center`,children:[N.type==="success"||N.type==="info"?s.jsx(St,{className:"mr-2"}):s.jsx(ml,{className:"mr-2"}),N.message]}),N.applied&&s.jsx(he,{to:"/candidate/applications",className:"mt-4 inline-block text-primary hover:underline",children:"View your applications"})]}),c&&c.role==="employer"&&c.id===ze&&s.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[s.jsx("h3",{class:"text-xl font-semibold text-gray-700 mb-3",children:"Employer Actions"}),s.jsxs(he,{to:`/employer/jobs/${n}/applications`,className:"btn btn-secondary",children:["View Applications (",m.applicationsCount||0,")"]})]})]})})]})},Tu=({title:n,value:c,icon:o,color:u})=>s.jsxs("div",{className:`bg-white p-6 rounded-lg shadow-lg flex items-center space-x-4 border-l-4 ${u}`,children:[s.jsx("div",{className:`p-3 rounded-full bg-opacity-20 ${u&&u.replace("border","bg").replace("-500","-100")}`,children:o}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500 font-medium",children:n}),s.jsx("p",{className:"text-2xl font-semibold text-gray-800",children:c})]})]}),G4=()=>{const{user:n}=wt(),[c,o]=R.useState([]),[u,f]=R.useState({totalJobs:0,totalApplications:0,totalViews:0}),[m,h]=R.useState(!0),[g,y]=R.useState(!0),[x,v]=R.useState(null);R.useEffect(()=>{(async()=>{var H,b;if(!(!n||n.role!=="employer")){h(!0),y(!0),v(null);try{const E=await Gl.getEmployerJobs();o(E.data.jobs||[]);const T=await d4.getJobPostingsAnalytics();f({totalJobs:T.data.totalJobsPosted||0,totalApplications:T.data.totalApplications||0,totalViews:T.data.totalViews||0})}catch(E){console.error("Error fetching employer data:",E),v(((b=(H=E.response)==null?void 0:H.data)==null?void 0:b.message)||"Failed to load dashboard data."),o([])}finally{h(!1),y(!1)}}})()},[n]);const N=async S=>{var H,b;if(window.confirm("Are you sure you want to delete this job posting? This action cannot be undone."))try{await Gl.deleteJob(S),o(E=>E.filter(T=>T._id!==S)),f(E=>({...E,totalJobs:E.totalJobs-1})),alert("Job deleted successfully.")}catch(E){console.error("Error deleting job:",E),alert(((b=(H=E.response)==null?void 0:H.data)==null?void 0:b.message)||"Failed to delete job.")}};return n?s.jsxs("div",{className:"container mx-auto py-8 px-4",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800",children:"Employer Dashboard"}),s.jsxs("p",{className:"text-gray-600 mt-1",children:["Welcome back, ",n.name,"!"]})]}),s.jsxs(he,{to:"/employer/post-job",className:"btn btn-primary mt-4 sm:mt-0 flex items-center",children:[s.jsx(Gu,{className:"mr-2"})," Post New Job"]})]}),x&&s.jsx("p",{className:"text-center text-red-500 bg-red-100 p-4 rounded-md mb-6",children:x}),s.jsxs("section",{className:"mb-10",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Overview"}),g?s.jsx("p",{children:"Loading statistics..."}):s.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[s.jsx(Tu,{title:"Total Jobs Posted",value:u.totalJobs,icon:s.jsx(Ut,{size:24,className:"text-blue-500"}),color:"border-blue-500"}),s.jsx(Tu,{title:"Total Applications",value:u.totalApplications,icon:s.jsx(er,{size:24,className:"text-green-500"}),color:"border-green-500"}),s.jsx(Tu,{title:"Total Job Views",value:u.totalViews,icon:s.jsx(wh,{size:24,className:"text-purple-500"}),color:"border-purple-500"})]})]}),s.jsxs("section",{children:[s.jsx("div",{className:"flex justify-between items-center mb-4",children:s.jsx("h2",{className:"text-2xl font-semibold text-gray-700",children:"Your Job Postings"})}),m?s.jsx("p",{children:"Loading your jobs..."}):c.length===0?s.jsxs("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:[s.jsx(Ut,{size:48,className:"mx-auto text-gray-400 mb-4"}),s.jsx("p",{className:"text-gray-600 text-lg mb-2",children:"You haven't posted any jobs yet."}),s.jsx(he,{to:"/employer/post-job",className:"btn btn-primary",children:"Post Your First Job"})]}):s.jsx("div",{className:"bg-white shadow-lg rounded-lg overflow-x-auto",children:s.jsxs("table",{className:"w-full table-auto",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applications"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Views"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Posted On"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(S=>s.jsxs("tr",{className:"hover:bg-gray-50 transition-colors",children:[s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[s.jsx(he,{to:`/jobs/${S._id}`,className:"text-sm font-medium text-primary hover:underline",children:S.title}),s.jsx("div",{className:"text-xs text-gray-500",children:S.location})]}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${S.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:S.isActive?"Active":"Inactive"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.jsx(he,{to:`/employer/jobs/${S._id}/applications`,className:"text-blue-600 hover:underline",children:S.applicationsCount||0})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:S.views||0}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(S.postedDate).toLocaleDateString()}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[s.jsx(he,{to:`/employer/jobs/${S._id}/edit`,className:"text-indigo-600 hover:text-indigo-900",title:"Edit Job",children:s.jsx(b4,{})}),s.jsx("button",{onClick:()=>N(S._id),className:"text-red-600 hover:text-red-900",title:"Delete Job",children:s.jsx(z4,{})}),s.jsx(he,{to:`/employer/jobs/${S._id}/applications`,className:"text-green-600 hover:text-green-900",title:"View Applications",children:s.jsx(ao,{})})]})]},S._id))})]})})]}),s.jsxs("section",{className:"mt-10",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Quick Actions"}),s.jsxs("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[s.jsxs(he,{to:"/employer/post-job",className:"block p-6 bg-blue-50 hover:bg-blue-100 rounded-lg shadow hover:shadow-md transition-all",children:[s.jsx(Gu,{size:28,className:"text-blue-600 mb-2"}),s.jsx("h3",{className:"text-lg font-semibold text-blue-700",children:"Post a New Job"}),s.jsx("p",{className:"text-sm text-blue-600",children:"Quickly create and publish a new job listing."})]}),s.jsxs(he,{to:"/employer/analytics",className:"block p-6 bg-green-50 hover:bg-green-100 rounded-lg shadow hover:shadow-md transition-all",children:[s.jsx(y4,{size:28,className:"text-green-600 mb-2"}),s.jsx("h3",{className:"text-lg font-semibold text-green-700",children:"View Analytics"}),s.jsx("p",{className:"text-sm text-green-600",children:"Track performance of your job postings."})]})]})]})]}):s.jsx("p",{className:"text-center py-10",children:"Please log in to view your dashboard."})},Ru=({title:n,value:c,icon:o,color:u,linkTo:f})=>s.jsx(he,{to:f,className:`block bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 ${u}`,children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`p-3 rounded-full bg-opacity-20 ${u&&u.replace("border","bg").replace("-500","-100")}`,children:o}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500 font-medium",children:n}),s.jsx("p",{className:"text-2xl font-semibold text-gray-800",children:c})]})]})}),X4=({status:n})=>{switch(n.toLowerCase()){case"applied":return s.jsx(ma,{className:"text-yellow-500",title:"Applied"});case"viewed":return s.jsx(wh,{className:"text-blue-500",title:"Viewed by Employer"});case"shortlisted":return s.jsx(St,{className:"text-teal-500",title:"Shortlisted"});case"interviewing":return s.jsx(ao,{className:"text-indigo-500",title:"Interviewing"});case"offered":return s.jsx(St,{className:"text-green-500",title:"Offered"});case"rejected":return s.jsx(ml,{className:"text-red-500",title:"Rejected"});case"withdrawn":return s.jsx(ml,{className:"text-gray-500",title:"Withdrawn"});default:return s.jsx(er,{className:"text-gray-400",title:n})}},J4=()=>{const{user:n}=wt(),[c,o]=R.useState([]),[u,f]=R.useState(null),[m,h]=R.useState(!0),[g,y]=R.useState(null);if(R.useEffect(()=>{(async()=>{var S,H;if(!(!n||n.role!=="candidate")){h(!0),y(null);try{const b=await da.getMyApplications();o(b.data||[]);try{const E=await Gs.getMyProfile();f(E.data)}catch(E){if(E.response&&E.response.status===404)f(null);else throw E}}catch(b){console.error("Error fetching candidate data:",b),y(((H=(S=b.response)==null?void 0:S.data)==null?void 0:H.message)||"Failed to load dashboard data."),o([]),f(null)}finally{h(!1)}}})()},[n]),m)return s.jsx("p",{className:"text-center py-10",children:"Loading your dashboard..."});if(!n)return s.jsx("p",{className:"text-center py-10",children:"Please log in to view your dashboard."});const x=c.length,v=c.filter(N=>N.status==="Shortlisted"||N.status==="Interviewing"||N.status==="Offered").length;return s.jsxs("div",{className:"container mx-auto py-8 px-4",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800",children:"Candidate Dashboard"}),s.jsxs("p",{className:"text-gray-600 mt-1",children:["Welcome back, ",n.name,"!"]})]}),s.jsxs(he,{to:"/jobs",className:"btn btn-primary mt-4 sm:mt-0 flex items-center",children:[s.jsx(oi,{className:"mr-2"})," Find New Jobs"]})]}),g&&s.jsx("p",{className:"text-center text-red-500 bg-red-100 p-4 rounded-md mb-6",children:g}),!u&&!m&&s.jsx("div",{className:"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded-md",role:"alert",children:s.jsxs("div",{className:"flex",children:[s.jsx("div",{className:"py-1",children:s.jsx(Xu,{className:"h-6 w-6 text-yellow-500 mr-3"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-bold",children:"Complete Your Profile"}),s.jsx("p",{className:"text-sm",children:"A complete profile increases your chances of getting noticed by employers. "}),s.jsx(he,{to:"/candidate/profile",className:"font-semibold underline hover:text-yellow-800",children:"Create or Update Your Profile Now"})]})]})}),s.jsxs("section",{className:"mb-10",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Your Activity"}),s.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[s.jsx(Ru,{title:"Applications Submitted",value:x,icon:s.jsx(er,{size:24,className:"text-blue-500"}),color:"border-blue-500",linkTo:"/candidate/applications"}),s.jsx(Ru,{title:"Active Processes",value:v,icon:s.jsx(E4,{size:24,className:"text-green-500"}),color:"border-green-500",linkTo:"/candidate/applications?status=active"}),s.jsx(Ru,{title:"Your Profile",value:u?"View/Edit":"Create Profile",icon:s.jsx(Xu,{size:24,className:"text-purple-500"}),color:"border-purple-500",linkTo:"/candidate/profile"})]})]}),s.jsxs("section",{children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Recent Applications"}),c.length===0&&!m?s.jsxs("div",{className:"text-center py-10 bg-gray-50 rounded-lg",children:[s.jsx(Ut,{size:48,className:"mx-auto text-gray-400 mb-4"}),s.jsx("p",{className:"text-gray-600 text-lg mb-2",children:"You haven't applied for any jobs yet."}),s.jsx(he,{to:"/jobs",className:"btn btn-primary",children:"Start Applying Now"})]}):s.jsxs("div",{className:"bg-white shadow-lg rounded-lg overflow-x-auto",children:[s.jsxs("table",{className:"w-full table-auto",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Company"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applied On"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.slice(0,5).map(N=>{var S,H,b,E,T;return s.jsxs("tr",{className:"hover:bg-gray-50 transition-colors",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx(he,{to:`/jobs/${((S=N.jobId)==null?void 0:S._id)||N.jobId}`,className:"text-sm font-medium text-primary hover:underline",children:((H=N.jobDetails)==null?void 0:H.title)||((b=N.jobSnapshot)==null?void 0:b.title)||"Job Title N/A"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:((E=N.jobDetails)==null?void 0:E.companyName)||((T=N.jobSnapshot)==null?void 0:T.companyName)||"Company N/A"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(N.applicationDate).toLocaleDateString()}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:s.jsxs("span",{className:"flex items-center",children:[s.jsx(X4,{status:N.status}),s.jsx("span",{className:"ml-2 capitalize",children:N.status})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsx(he,{to:`/candidate/applications/${N._id}`,className:"text-indigo-600 hover:text-indigo-900",children:"View Details"})})]},N._id)})})]}),c.length>5&&s.jsx("div",{className:"text-center p-4 border-t border-gray-200",children:s.jsxs(he,{to:"/candidate/applications",className:"text-primary hover:underline font-medium",children:["View All Applications (",c.length,")"]})})]})]})]})},Q4=()=>{const{user:n}=wt(),c=Xl(),[o,u]=R.useState({title:"",companyName:(n==null?void 0:n.companyName)||"",location:"",jobType:"Full-time",salary:"",salaryPeriod:"Annually",description:"",requirements:[""],skills:[""]}),[f,m]=R.useState(!1),[h,g]=R.useState(null),[y,x]=R.useState(null),v=z=>{const{name:M,value:J}=z.target;u(k=>({...k,[M]:J}))},N=(z,M,J)=>{const k=[...o[z]];k[M]=J,u(Q=>({...Q,[z]:k}))},S=z=>{u(M=>({...M,[z]:[...M[z],""]}))},H=(z,M)=>{const J=o[z].filter((k,Q)=>Q!==M);u(k=>({...k,[z]:J}))},b=async z=>{var J,k;if(z.preventDefault(),m(!0),g(null),x(null),!o.title.trim()||!o.description.trim()){g("Job title and description are required."),m(!1);return}if(!o.companyName.trim()&&!(n!=null&&n.companyName)){g("Company name is required. Please ensure it is set in your profile or entered here."),m(!1);return}const M={...o,requirements:o.requirements.filter(Q=>Q.trim()!==""),skills:o.skills.filter(Q=>Q.trim()!==""),salary:o.salary?parseFloat(o.salary):void 0};try{const Q=await Gl.createJob(M);x(`Job "${Q.data.title}" posted successfully!`),u({title:"",companyName:(n==null?void 0:n.companyName)||"",location:"",jobType:"Full-time",salary:"",salaryPeriod:"Annually",description:"",requirements:[""],skills:[""]}),setTimeout(()=>{c(`/jobs/${Q.data._id}`)},1500)}catch(Q){console.error("Error posting job:",Q),g(((k=(J=Q.response)==null?void 0:J.data)==null?void 0:k.message)||"Failed to post job. Please check your input and try again.")}finally{m(!1)}},E="input-field w-full",T="block text-sm font-medium text-gray-700 mb-1";return s.jsx("div",{className:"container mx-auto py-8 px-4",children:s.jsxs("div",{className:"max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-2xl",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx(Ut,{className:"mx-auto h-12 w-auto text-primary"}),s.jsx("h1",{className:"text-3xl font-bold text-gray-800 mt-4",children:"Post a New Job"}),s.jsx("p",{className:"text-gray-600",children:"Fill in the details below to find your next great hire."})]}),h&&s.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6",role:"alert",children:h}),y&&s.jsx("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6",role:"alert",children:y}),s.jsxs("form",{onSubmit:b,className:"space-y-6",children:[s.jsxs("div",{children:[s.jsxs("label",{htmlFor:"title",className:T,children:["Job Title ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("input",{type:"text",name:"title",id:"title",value:o.title,onChange:v,className:E,placeholder:"e.g., Senior Software Engineer",required:!0})]}),s.jsxs("div",{children:[s.jsxs("label",{htmlFor:"companyName",className:T,children:["Company Name ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("input",{type:"text",name:"companyName",id:"companyName",value:o.companyName,onChange:v,className:E,placeholder:"Your company's name",required:!0})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"location",className:T,children:"Location"}),s.jsx("input",{type:"text",name:"location",id:"location",value:o.location,onChange:v,className:E,placeholder:"e.g., San Francisco, CA or Remote"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"jobType",className:T,children:"Job Type"}),s.jsxs("select",{name:"jobType",id:"jobType",value:o.jobType,onChange:v,className:E,children:[s.jsx("option",{value:"Full-time",children:"Full-time"}),s.jsx("option",{value:"Part-time",children:"Part-time"}),s.jsx("option",{value:"Contract",children:"Contract"}),s.jsx("option",{value:"Internship",children:"Internship"}),s.jsx("option",{value:"Temporary",children:"Temporary"})]})]})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"salary",className:T,children:"Salary (Optional)"}),s.jsx("input",{type:"number",name:"salary",id:"salary",value:o.salary,onChange:v,className:E,placeholder:"e.g., 90000"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"salaryPeriod",className:T,children:"Salary Period"}),s.jsxs("select",{name:"salaryPeriod",id:"salaryPeriod",value:o.salaryPeriod,onChange:v,className:E,disabled:!o.salary,children:[s.jsx("option",{value:"Annually",children:"Annually"}),s.jsx("option",{value:"Monthly",children:"Monthly"}),s.jsx("option",{value:"Hourly",children:"Hourly"})]})]})]}),s.jsxs("div",{children:[s.jsxs("label",{htmlFor:"description",className:T,children:["Job Description ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("textarea",{name:"description",id:"description",value:o.description,onChange:v,rows:"6",className:E,placeholder:"Detailed description of the job role, responsibilities, and company culture.",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:T,children:"Requirements"}),o.requirements.map((z,M)=>s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx("input",{type:"text",value:z,onChange:J=>N("requirements",M,J.target.value),className:E,placeholder:`Requirement ${M+1}`}),o.requirements.length>1&&s.jsx("button",{type:"button",onClick:()=>H("requirements",M),className:"text-red-500 hover:text-red-700 p-1",children:"Remove"})]},M)),s.jsx("button",{type:"button",onClick:()=>S("requirements"),className:"text-sm text-primary hover:underline",children:"+ Add Requirement"})]}),s.jsxs("div",{children:[s.jsx("label",{className:T,children:"Skills"}),o.skills.map((z,M)=>s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx("input",{type:"text",value:z,onChange:J=>N("skills",M,J.target.value),className:E,placeholder:`Skill ${M+1} (e.g., React, Node.js)`}),o.skills.length>1&&s.jsx("button",{type:"button",onClick:()=>H("skills",M),className:"text-red-500 hover:text-red-700 p-1",children:"Remove"})]},M)),s.jsx("button",{type:"button",onClick:()=>S("skills"),className:"text-sm text-primary hover:underline",children:"+ Add Skill"})]}),s.jsx("div",{className:"pt-4",children:s.jsxs("button",{type:"submit",disabled:f,className:"w-full btn btn-primary py-3 text-lg flex items-center justify-center disabled:opacity-70",children:[s.jsx(Vu,{className:"mr-2"})," ",f?"Posting Job...":"Post Job Listing"]})})]})]})})},k4=()=>{const{user:n,setUser:c}=wt(),[o,u]=R.useState(null),[f,m]=R.useState({fullName:"",contactNumber:"",headline:"",summary:"",portfolioUrl:"",linkedinUrl:"",githubUrl:"",experiences:[],educations:[],skills:[],resume:null,resumeUrl:""}),[h,g]=R.useState(!0),[y,x]=R.useState(!1),[v,N]=R.useState(null),[S,H]=R.useState(null),[b,E]=R.useState(null),[T,z]=R.useState(null),[M,J]=R.useState(null),k=R.useCallback(async()=>{var W,_;if(!n||n.role!=="candidate"){g(!1),N("User not authorized or not a candidate.");return}g(!0);try{const X=await Gs.getMyProfile();u(X.data),m({fullName:X.data.fullName||n.name||"",contactNumber:X.data.contactNumber||"",headline:X.data.headline||"",summary:X.data.summary||"",portfolioUrl:X.data.portfolioUrl||"",linkedinUrl:X.data.linkedinUrl||"",githubUrl:X.data.githubUrl||"",experiences:X.data.experiences||[],educations:X.data.educations||[],skills:X.data.skills||[],resumeUrl:X.data.resumeUrl||"",resume:null}),N(null)}catch(X){console.error("Failed to fetch profile:",X),X.response&&X.response.status===404?(m(P=>({...P,fullName:n.name||""})),u(null)):N(((_=(W=X.response)==null?void 0:W.data)==null?void 0:_.message)||"Failed to load profile.")}finally{g(!1)}},[n]);R.useEffect(()=>{k()},[k]);const Q=W=>{const{name:_,value:X,files:P}=W.target;m(_==="resume"?te=>({...te,resume:P[0]}):te=>({...te,[_]:X}))},Z=W=>{const{name:_,value:X}=W.target;z(P=>({...P,[_]:X}))},le=(W,_=null,X=null)=>{E(W),J(X),_?z({..._}):(W==="experiences"&&z({title:"",company:"",startDate:"",endDate:"",description:""}),W==="educations"&&z({institution:"",degree:"",fieldOfStudy:"",startDate:"",endDate:""}),W==="skills"&&z({name:"",proficiency:"Intermediate"}))},xe=()=>{E(null),z(null),J(null)},oe=()=>{if(!T)return;let W;M!==null?W=f[b].map((_,X)=>X===M?T:_):W=[...f[b],T],m(_=>({..._,[b]:W})),xe()},ze=(W,_)=>{m(X=>({...X,[W]:X[W].filter((P,te)=>te!==_)}))},ee=async W=>{var X,P;W.preventDefault(),x(!0),N(null),H(null);const _=new FormData;Object.keys(f).forEach(te=>{te==="experiences"||te==="educations"||te==="skills"?f[te].forEach((w,G)=>{Object.keys(w).forEach(F=>{_.append(`${te}[${G}][${F}]`,w[F])})}):te==="resume"&&f.resume?_.append("resume",f.resume):te!=="resume"&&te!=="resumeUrl"&&_.append(te,f[te])});try{const te=await Gs.updateMyProfile(_);u(te.data.profile||te.data),m(w=>{var G;return{...w,resume:null,resumeUrl:((G=te.data.profile)==null?void 0:G.resumeUrl)||te.data.resumeUrl||w.resumeUrl}}),H("Profile saved successfully!"),te.data.user&&c(te.data.user),setTimeout(()=>H(null),3e3)}catch(te){console.error("Failed to save profile:",te),N(((P=(X=te.response)==null?void 0:X.data)==null?void 0:P.message)||"Failed to save profile. Please try again."),setTimeout(()=>N(null),5e3)}finally{x(!1)}},ae="input-field w-full",re="block text-sm font-medium text-gray-700 mb-1",Me="bg-white p-6 rounded-lg shadow-md";return h?s.jsxs("div",{className:"flex justify-center items-center h-screen",children:[s.jsx(ha,{className:"animate-spin text-4xl text-primary"})," ",s.jsx("span",{className:"ml-3 text-xl",children:"Loading Profile..."})]}):!n||n.role!=="candidate"?s.jsx("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:"You are not authorized to view this page. Please log in as a candidate."}):s.jsxs("div",{className:"container mx-auto py-10 px-4",children:[s.jsxs("div",{className:"max-w-4xl mx-auto bg-gradient-to-br from-gray-50 to-blue-50 p-8 rounded-xl shadow-2xl",children:[s.jsxs("div",{className:"text-center mb-10",children:[s.jsx(Xu,{className:"mx-auto h-16 w-auto text-primary"}),s.jsx("h1",{className:"text-4xl font-bold text-gray-800 mt-4",children:"Manage Your Profile"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"Keep your professional information up-to-date to attract employers."})]}),v&&s.jsxs("div",{className:"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded",role:"alert",children:[s.jsx(ml,{className:"inline mr-2"}),v]}),S&&s.jsxs("div",{className:"bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded",role:"alert",children:[s.jsx(St,{className:"inline mr-2"}),S]}),s.jsxs("form",{onSubmit:ee,className:"space-y-8",children:[s.jsxs("div",{className:Me,children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-6",children:"Basic Information"}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsxs("label",{htmlFor:"fullName",className:re,children:["Full Name ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("input",{type:"text",name:"fullName",id:"fullName",value:f.fullName,onChange:Q,className:ae,required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"contactNumber",className:re,children:"Contact Number"}),s.jsx("input",{type:"tel",name:"contactNumber",id:"contactNumber",value:f.contactNumber,onChange:Q,className:ae})]})]}),s.jsxs("div",{className:"mt-6",children:[s.jsxs("label",{htmlFor:"headline",className:re,children:["Headline ",s.jsx("span",{className:"text-red-500",children:"*"})]}),s.jsx("input",{type:"text",name:"headline",id:"headline",value:f.headline,onChange:Q,className:ae,placeholder:"e.g., Full Stack Developer | React & Node.js Expert",required:!0})]}),s.jsxs("div",{className:"mt-6",children:[s.jsx("label",{htmlFor:"summary",className:re,children:"Summary"}),s.jsx("textarea",{name:"summary",id:"summary",value:f.summary,onChange:Q,rows:"5",className:ae,placeholder:"Brief professional summary..."})]})]}),s.jsxs("div",{className:Me,children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-6",children:"Professional Links"}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"portfolioUrl",className:re,children:"Portfolio URL"}),s.jsx("input",{type:"url",name:"portfolioUrl",id:"portfolioUrl",value:f.portfolioUrl,onChange:Q,className:ae,placeholder:"https://yourportfolio.com"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"linkedinUrl",className:re,children:"LinkedIn Profile URL"}),s.jsx("input",{type:"url",name:"linkedinUrl",id:"linkedinUrl",value:f.linkedinUrl,onChange:Q,className:ae,placeholder:"https://linkedin.com/in/yourprofile"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"githubUrl",className:re,children:"GitHub Profile URL"}),s.jsx("input",{type:"url",name:"githubUrl",id:"githubUrl",value:f.githubUrl,onChange:Q,className:ae,placeholder:"https://github.com/yourusername"})]})]})]}),s.jsxs("div",{className:Me,children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700",children:"Work Experience"}),s.jsx("button",{type:"button",onClick:()=>le("experiences"),className:"btn btn-secondary btn-sm",children:"+ Add Experience"})]}),f.experiences.length===0&&s.jsx("p",{className:"text-gray-500",children:"No work experience added yet."}),s.jsx("div",{className:"space-y-4",children:f.experiences.map((W,_)=>s.jsxs("div",{className:"p-4 border rounded-md bg-gray-50",children:[s.jsxs("h3",{className:"font-semibold text-lg",children:[W.title," at ",W.company]}),s.jsxs("p",{className:"text-sm text-gray-600",children:[W.startDate," - ",W.endDate||"Present"]}),s.jsx("p",{className:"text-sm mt-1 whitespace-pre-wrap",children:W.description}),s.jsxs("div",{className:"mt-2 space-x-2",children:[s.jsx("button",{type:"button",onClick:()=>le("experiences",W,_),className:"text-sm text-blue-600 hover:underline",children:"Edit"}),s.jsx("button",{type:"button",onClick:()=>ze("experiences",_),className:"text-sm text-red-600 hover:underline",children:"Remove"})]})]},_))})]}),s.jsxs("div",{className:Me,children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700",children:"Education"}),s.jsx("button",{type:"button",onClick:()=>le("educations"),className:"btn btn-secondary btn-sm",children:"+ Add Education"})]}),f.educations.length===0&&s.jsx("p",{className:"text-gray-500",children:"No education details added yet."}),s.jsx("div",{className:"space-y-4",children:f.educations.map((W,_)=>s.jsxs("div",{className:"p-4 border rounded-md bg-gray-50",children:[s.jsxs("h3",{className:"font-semibold text-lg",children:[W.degree," in ",W.fieldOfStudy]}),s.jsx("p",{className:"text-sm text-gray-600",children:W.institution}),s.jsxs("p",{className:"text-sm text-gray-500",children:[W.startDate," - ",W.endDate||"Present"]}),s.jsxs("div",{className:"mt-2 space-x-2",children:[s.jsx("button",{type:"button",onClick:()=>le("educations",W,_),className:"text-sm text-blue-600 hover:underline",children:"Edit"}),s.jsx("button",{type:"button",onClick:()=>ze("educations",_),className:"text-sm text-red-600 hover:underline",children:"Remove"})]})]},_))})]}),s.jsxs("div",{className:Me,children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700",children:"Skills"}),s.jsx("button",{type:"button",onClick:()=>le("skills"),className:"btn btn-secondary btn-sm",children:"+ Add Skill"})]}),f.skills.length===0&&s.jsx("p",{className:"text-gray-500",children:"No skills added yet."}),s.jsx("div",{className:"flex flex-wrap gap-2",children:f.skills.map((W,_)=>s.jsxs("div",{className:"bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center",children:[W.name," (",W.proficiency,")",s.jsx("button",{type:"button",onClick:()=>le("skills",W,_),className:"ml-2 text-xs text-blue-500 hover:text-blue-700",children:"(edit)"}),s.jsx("button",{type:"button",onClick:()=>ze("skills",_),className:"ml-1 text-xs text-red-500 hover:text-red-700",children:"×"})]},_))})]}),s.jsxs("div",{className:Me,children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Resume/CV"}),s.jsx("label",{htmlFor:"resume",className:re,children:"Upload New Resume (PDF, DOC, DOCX)"}),s.jsx("input",{type:"file",name:"resume",id:"resume",onChange:Q,className:"w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-light file:text-primary hover:file:bg-primary-dark",accept:".pdf,.doc,.docx"}),f.resumeUrl&&s.jsxs("p",{className:"mt-3 text-sm text-gray-600",children:["Current Resume: ",s.jsx("a",{href:f.resumeUrl,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"View Resume"})]}),f.resume&&s.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["Selected: ",f.resume.name]})]}),s.jsx("div",{className:"pt-6",children:s.jsxs("button",{type:"submit",disabled:y,className:"w-full btn btn-primary py-3 text-lg flex items-center justify-center disabled:opacity-70",children:[y?s.jsx(ha,{className:"animate-spin mr-2"}):s.jsx(St,{className:"mr-2"}),y?"Saving Profile...":"Save Profile"]})})]})]}),b&&T&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:s.jsxs("div",{className:"bg-white p-8 rounded-lg shadow-xl max-w-lg w-full",children:[s.jsxs("h3",{className:"text-xl font-semibold mb-4 capitalize",children:[M!==null?"Edit":"Add"," ",b.slice(0,-1)]}),b==="experiences"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Job Title"}),s.jsx("input",{type:"text",name:"title",value:T.title,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Company"}),s.jsx("input",{type:"text",name:"company",value:T.company,onChange:Z,className:ae})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Start Date"}),s.jsx("input",{type:"month",name:"startDate",value:T.startDate,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"End Date (leave blank if current)"}),s.jsx("input",{type:"month",name:"endDate",value:T.endDate,onChange:Z,className:ae})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Description"}),s.jsx("textarea",{name:"description",value:T.description,onChange:Z,rows:"4",className:ae})]})]}),b==="educations"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Institution"}),s.jsx("input",{type:"text",name:"institution",value:T.institution,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Degree"}),s.jsx("input",{type:"text",name:"degree",value:T.degree,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Field of Study"}),s.jsx("input",{type:"text",name:"fieldOfStudy",value:T.fieldOfStudy,onChange:Z,className:ae})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Start Date"}),s.jsx("input",{type:"month",name:"startDate",value:T.startDate,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"End Date (leave blank if current)"}),s.jsx("input",{type:"month",name:"endDate",value:T.endDate,onChange:Z,className:ae})]})]})]}),b==="skills"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Skill Name"}),s.jsx("input",{type:"text",name:"name",value:T.name,onChange:Z,className:ae})]}),s.jsxs("div",{children:[s.jsx("label",{className:re,children:"Proficiency"}),s.jsxs("select",{name:"proficiency",value:T.proficiency,onChange:Z,className:ae,children:[s.jsx("option",{value:"Beginner",children:"Beginner"}),s.jsx("option",{value:"Intermediate",children:"Intermediate"}),s.jsx("option",{value:"Advanced",children:"Advanced"}),s.jsx("option",{value:"Expert",children:"Expert"})]})]})]}),s.jsxs("div",{className:"mt-6 flex justify-end space-x-3",children:[s.jsx("button",{type:"button",onClick:xe,className:"btn btn-ghost",children:"Cancel"}),s.jsx("button",{type:"button",onClick:oe,className:"btn btn-primary",children:"Save Item"})]})]})})]})},Z4=()=>{const{user:n}=wt(),[c,o]=R.useState([]),[u,f]=R.useState(!0),[m,h]=R.useState(null),[g,y]=R.useState(""),[x,v]=R.useState("all"),N=R.useCallback(async()=>{var b,E;if(!n||n.role!=="candidate"){h("You must be logged in as a candidate to view applications."),f(!1);return}f(!0);try{const T=await da.getMyApplications();o(T.data),h(null)}catch(T){console.error("Failed to fetch applications:",T),h(((E=(b=T.response)==null?void 0:b.data)==null?void 0:E.message)||"Failed to load applications.")}finally{f(!1)}},[n]);R.useEffect(()=>{N()},[N]);const S=b=>{switch(b.toLowerCase()){case"pending":return{icon:s.jsx(ma,{className:"text-yellow-500"}),color:"text-yellow-600 bg-yellow-100"};case"accepted":case"shortlisted":return{icon:s.jsx(St,{className:"text-green-500"}),color:"text-green-600 bg-green-100"};case"rejected":return{icon:s.jsx(ml,{className:"text-red-500"}),color:"text-red-600 bg-red-100"};default:return{icon:s.jsx(ma,{className:"text-gray-500"}),color:"text-gray-600 bg-gray-100"}}},H=c.filter(b=>{var E,T,z,M;return((T=(E=b.job)==null?void 0:E.title)==null?void 0:T.toLowerCase().includes(g.toLowerCase()))||((M=(z=b.job)==null?void 0:z.companyName)==null?void 0:M.toLowerCase().includes(g.toLowerCase()))}).filter(b=>x==="all"||b.status.toLowerCase()===x);return u?s.jsxs("div",{className:"flex justify-center items-center h-screen",children:[s.jsx(ha,{className:"animate-spin text-4xl text-primary"})," ",s.jsx("span",{className:"ml-3 text-xl",children:"Loading Applications..."})]}):m?s.jsxs("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:["Error: ",m]}):!n||n.role!=="candidate"?s.jsx("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:"Please log in as a candidate to view your applications."}):s.jsx("div",{className:"container mx-auto py-10 px-4 min-h-screen",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsxs("div",{className:"text-center mb-10",children:[s.jsx(Ut,{className:"mx-auto h-12 w-auto text-primary"}),s.jsx("h1",{className:"text-4xl font-bold text-gray-800 mt-4",children:"My Job Applications"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"Track the status of all your job applications here."})]}),s.jsx("div",{className:"mb-8 p-6 bg-white rounded-lg shadow-md",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-4 items-end",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Applications"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",id:"search",placeholder:"Search by job title or company...",className:"input-field w-full pl-10",value:g,onChange:b=>y(b.target.value)}),s.jsx(oi,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"statusFilter",className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Status"}),s.jsxs("select",{id:"statusFilter",className:"input-field w-full",value:x,onChange:b=>v(b.target.value),children:[s.jsx("option",{value:"all",children:"All Statuses"}),s.jsx("option",{value:"pending",children:"Pending"}),s.jsx("option",{value:"shortlisted",children:"Shortlisted"}),s.jsx("option",{value:"accepted",children:"Accepted"}),s.jsx("option",{value:"rejected",children:"Rejected"})]})]})]})}),H.length===0?s.jsxs("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[s.jsx(Ut,{className:"mx-auto text-6xl text-gray-300 mb-4"}),s.jsx("p",{className:"text-xl text-gray-500",children:"You haven't applied for any jobs yet, or no applications match your filters."}),s.jsx(he,{to:"/jobs",className:"mt-4 inline-block btn btn-primary",children:"Find Jobs to Apply"})]}):s.jsx("div",{className:"space-y-6",children:H.map(b=>{var z,M,J,k;const{icon:E,color:T}=S(b.status);return s.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-semibold text-primary hover:underline",children:s.jsx(he,{to:`/jobs/${(z=b.job)==null?void 0:z._id}`,children:((M=b.job)==null?void 0:M.title)||"Job Title Not Available"})}),s.jsx("p",{className:"text-gray-700 text-md",children:((J=b.job)==null?void 0:J.companyName)||"Company Not Available"}),s.jsxs("p",{className:"text-sm text-gray-500 flex items-center mt-1",children:[s.jsx(to,{className:"mr-2"})," Applied on: ",new Date(b.applicationDate).toLocaleDateString()]})]}),s.jsxs("div",{className:`mt-4 md:mt-0 px-3 py-1.5 rounded-full text-sm font-medium flex items-center ${T}`,children:[E,s.jsx("span",{className:"ml-2 capitalize",children:b.status})]})]}),b.coverLetter&&s.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[s.jsx("h4",{className:"font-semibold text-gray-700",children:"Cover Letter Snippet:"}),s.jsxs("p",{className:"text-sm text-gray-600 italic mt-1 truncate",children:[b.coverLetter.substring(0,150),b.coverLetter.length>150?"...":""]})]}),s.jsx("div",{className:"mt-4 text-right",children:s.jsx(he,{to:`/jobs/${(k=b.job)==null?void 0:k._id}`,className:"btn btn-outline btn-sm",children:"View Job Details"})})]},b._id)})})]})})},K4=()=>{const{applicationId:n}=Js(),{user:c,loading:o}=wt(),[u,f]=R.useState(null),[m,h]=R.useState(!0),[g,y]=R.useState(null);R.useEffect(()=>{o||(async()=>{var M,J;if(!c||c.role!=="candidate"){y("You must be logged in as a candidate."),h(!1);return}if(!n){y("Application ID is missing."),h(!1);return}h(!0);try{const k=await da.getApplicationById(n);f(k.data),y(null)}catch(k){console.error("Failed to fetch application details:",k),y(((J=(M=k.response)==null?void 0:M.data)==null?void 0:J.message)||"Failed to load application details."),f(null)}finally{h(!1)}})()},[n,c,o]);const x=z=>{switch(z=z==null?void 0:z.toLowerCase(),z){case"pending":return{text:"Pending",icon:s.jsx(ma,{className:"mr-2 text-yellow-500"}),color:"text-yellow-700 bg-yellow-100"};case"viewed":return{text:"Viewed by Employer",icon:s.jsx(ma,{className:"mr-2 text-blue-500"}),color:"text-blue-700 bg-blue-100"};case"shortlisted":return{text:"Shortlisted",icon:s.jsx(St,{className:"mr-2 text-teal-500"}),color:"text-teal-700 bg-teal-100"};case"interviewing":return{text:"Interviewing",icon:s.jsx(St,{className:"mr-2 text-indigo-500"}),color:"text-indigo-700 bg-indigo-100"};case"offered":return{text:"Offer Extended",icon:s.jsx(St,{className:"mr-2 text-green-500"}),color:"text-green-700 bg-green-100"};case"accepted":return{text:"Offer Accepted",icon:s.jsx(St,{className:"mr-2 text-green-600"}),color:"text-green-800 bg-green-200"};case"rejected":return{text:"Not Selected",icon:s.jsx(ml,{className:"mr-2 text-red-500"}),color:"text-red-700 bg-red-100"};case"withdrawn":return{text:"Application Withdrawn",icon:s.jsx(ml,{className:"mr-2 text-gray-500"}),color:"text-gray-700 bg-gray-100"};default:return{text:z||"Unknown",icon:s.jsx(er,{className:"mr-2 text-gray-400"}),color:"text-gray-700 bg-gray-100"}}};if(o||m)return s.jsxs("div",{className:"flex justify-center items-center h-screen",children:[s.jsx(ha,{className:"animate-spin text-4xl text-primary"})," ",s.jsx("span",{className:"ml-3 text-xl",children:"Loading Application Details..."})]});if(g)return s.jsxs("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:["Error: ",g]});if(!u)return s.jsx("div",{className:"container mx-auto py-8 px-4 text-center text-gray-600",children:"Application not found."});const{jobDetails:v,jobSnapshot:N,applicationDate:S,status:H,coverLetter:b}=u,E=v||N,T=x(H);return s.jsx("div",{className:"container mx-auto py-10 px-4 min-h-screen",children:s.jsxs("div",{className:"max-w-3xl mx-auto bg-white shadow-xl rounded-lg p-8",children:[s.jsx("div",{className:"mb-6",children:s.jsxs(he,{to:"/candidate/applications",className:"text-primary hover:underline flex items-center",children:[s.jsx(qu,{className:"mr-2"})," Back to My Applications"]})}),s.jsxs("div",{className:"border-b pb-6 mb-6",children:[s.jsxs("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:["Application for: ",(E==null?void 0:E.title)||"Job Title Not Available"]}),s.jsxs("p",{className:"text-xl text-gray-600 flex items-center mb-1",children:[s.jsx(pi,{className:"mr-2 text-gray-500"})," ",(E==null?void 0:E.companyName)||"Company Not Available"]}),s.jsxs("p",{className:"text-sm text-gray-500 flex items-center",children:[s.jsx(to,{className:"mr-2 text-gray-400"})," Applied on: ",new Date(S).toLocaleDateString()]})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Application Status"}),s.jsxs("div",{className:`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${T.color}`,children:[T.icon,T.text]})]}),b&&s.jsxs("div",{className:"mb-6",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Your Cover Letter"}),s.jsx("div",{className:"p-4 bg-gray-50 rounded-md prose max-w-none text-gray-700 whitespace-pre-wrap",children:b})]}),E&&s.jsxs("div",{className:"mb-6",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Job Details Snapshot"}),s.jsxs("div",{className:"p-4 bg-gray-50 rounded-md space-y-2",children:[s.jsxs("p",{children:[s.jsx("strong",{children:"Location:"})," ",E.location||"N/A"]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Type:"})," ",E.jobType||"N/A"]}),E.salary&&s.jsxs("p",{children:[s.jsx("strong",{children:"Salary:"}),typeof E.salary=="number"?`$${E.salary.toLocaleString()}`:E.salary,E.salaryPeriod&&` per ${E.salaryPeriod}`]}),s.jsx("div",{className:"mt-3",children:s.jsx(he,{to:`/jobs/${E._id}`,className:"btn btn-outline btn-sm text-primary border-primary hover:bg-primary hover:text-white",children:"View Original Job Posting"})})]})]})]})})},F4=()=>{const{jobId:n}=Js(),{user:c}=wt(),[o,u]=R.useState([]),[f,m]=R.useState(null),[h,g]=R.useState(!0),[y,x]=R.useState(null),[v,N]=R.useState({}),[S,H]=R.useState("all"),b=R.useCallback(async()=>{var M,J;if(!c||c.role!=="employer"){x("You must be logged in as an employer."),g(!1);return}g(!0);try{const[k,Q]=await Promise.all([Gl.getJobById(n),da.getJobApplications(n)]);m(k.data),k.data.employer._id!==c.id&&k.data.employer!==c.id?(x("You are not authorized to view applications for this job."),u([])):u(Q.data),x(null)}catch(k){console.error("Failed to fetch job and applications:",k),x(((J=(M=k.response)==null?void 0:M.data)==null?void 0:J.message)||"Failed to load data.")}finally{g(!1)}},[n,c]);R.useEffect(()=>{b()},[b]);const E=async(M,J)=>{var k,Q;N(Z=>({...Z,[M]:!0}));try{await da.updateApplicationStatus(M,J),u(Z=>Z.map(le=>le._id===M?{...le,status:J}:le))}catch(Z){console.error("Failed to update status:",Z),alert(`Error updating status: ${((Q=(k=Z.response)==null?void 0:k.data)==null?void 0:Q.message)||"Please try again."}`)}finally{N(Z=>({...Z,[M]:!1}))}},T=M=>{switch(M==null?void 0:M.toLowerCase()){case"pending":return{icon:s.jsx(ma,{className:"text-yellow-500"}),color:"border-yellow-500",bgColor:"bg-yellow-50"};case"shortlisted":return{icon:s.jsx(g4,{className:"text-blue-500"}),color:"border-blue-500",bgColor:"bg-blue-50"};case"accepted":return{icon:s.jsx(St,{className:"text-green-500"}),color:"border-green-500",bgColor:"bg-green-50"};case"rejected":return{icon:s.jsx(Ch,{className:"text-red-500"}),color:"border-red-500",bgColor:"bg-red-50"};default:return{icon:s.jsx(ma,{className:"text-gray-500"}),color:"border-gray-500",bgColor:"bg-gray-50"}}},z=o.filter(M=>S==="all"||M.status.toLowerCase()===S);return h?s.jsxs("div",{className:"flex justify-center items-center h-screen",children:[s.jsx(ha,{className:"animate-spin text-4xl text-primary"})," ",s.jsx("span",{className:"ml-3 text-xl",children:"Loading Applications..."})]}):y?s.jsxs("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:["Error: ",y]}):!c||c.role!=="employer"?s.jsx("div",{className:"container mx-auto py-8 px-4 text-center text-red-600",children:"Access Denied. Please log in as an employer."}):f?s.jsx("div",{className:"container mx-auto py-10 px-4 min-h-screen",children:s.jsxs("div",{className:"max-w-6xl mx-auto",children:[s.jsxs("div",{className:"mb-8 p-6 bg-white rounded-lg shadow-md",children:[s.jsxs("h1",{className:"text-3xl font-bold text-primary mb-2",children:["Applications for: ",f.title]}),s.jsx("p",{className:"text-gray-600",children:"Manage and review candidates who applied for this position."}),s.jsx(he,{to:`/jobs/${n}`,className:"text-sm text-blue-600 hover:underline mt-1 inline-block",children:"View Job Posting"})]}),s.jsxs("div",{className:"mb-6 p-4 bg-white rounded-lg shadow-sm",children:[s.jsxs("label",{htmlFor:"statusFilter",className:"block text-sm font-medium text-gray-700 mb-1 flex items-center",children:[s.jsx(Ah,{className:"mr-2"})," Filter by Status:"]}),s.jsxs("select",{id:"statusFilter",className:"input-field w-full md:w-1/3",value:S,onChange:M=>H(M.target.value),children:[s.jsx("option",{value:"all",children:"All Applications"}),s.jsx("option",{value:"pending",children:"Pending"}),s.jsx("option",{value:"shortlisted",children:"Shortlisted"}),s.jsx("option",{value:"accepted",children:"Accepted"}),s.jsx("option",{value:"rejected",children:"Rejected"})]})]}),z.length===0?s.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow",children:[s.jsx(O4,{className:"mx-auto text-6xl text-gray-300 mb-4"}),s.jsx("p",{className:"text-xl text-gray-500",children:o.length===0?"No applications received for this job yet.":"No applications match the current filter."})]}):s.jsx("div",{className:"space-y-6",children:z.map(M=>{var le,xe;const{icon:J,color:k,bgColor:Q}=T(M.status),Z=M.candidate;return s.jsxs("div",{className:`bg-white p-6 rounded-lg shadow-lg border-l-4 ${k} ${Q} transition-all duration-300 hover:shadow-xl`,children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:(Z==null?void 0:Z.fullName)||((le=Z==null?void 0:Z.user)==null?void 0:le.name)||"N/A"}),(Z==null?void 0:Z.headline)&&s.jsx("p",{className:"text-md text-primary italic",children:Z.headline}),s.jsxs("div",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[((xe=Z==null?void 0:Z.user)==null?void 0:xe.email)&&s.jsxs("p",{className:"flex items-center",children:[s.jsx(Is,{className:"mr-2 text-gray-400"})," ",Z.user.email]}),(Z==null?void 0:Z.contactNumber)&&s.jsxs("p",{className:"flex items-center",children:[s.jsx(Th,{className:"mr-2 text-gray-400"})," ",Z.contactNumber]}),s.jsxs("p",{className:"flex items-center",children:[s.jsx(to,{className:"mr-2 text-gray-400"})," Applied on: ",new Date(M.applicationDate).toLocaleDateString()]})]})]}),s.jsxs("div",{className:`mt-4 md:mt-0 px-3 py-1.5 rounded-full text-sm font-medium flex items-center ${Q.replace("bg-","text-").replace("-50","-700")} border ${k}`,children:[J,s.jsx("span",{className:"ml-2 capitalize",children:M.status})]})]}),M.coverLetter&&s.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[s.jsx("h4",{className:"font-semibold text-gray-700",children:"Cover Letter:"}),s.jsx("p",{className:"text-sm text-gray-600 mt-1 whitespace-pre-wrap",children:M.coverLetter})]}),(Z==null?void 0:Z.resumeUrl)&&s.jsx("div",{className:"mt-4",children:s.jsxs("a",{href:Z.resumeUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center btn btn-outline btn-sm text-primary border-primary hover:bg-primary hover:text-white",children:[s.jsx(v4,{className:"mr-2"})," View Resume"]})}),(Z==null?void 0:Z._id)&&s.jsx("div",{className:"mt-3",children:s.jsx(he,{to:`/candidate/${Z._id}/profile`,className:"text-sm text-blue-600 hover:underline",children:"View Full Profile"})}),s.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[s.jsx("h4",{className:"font-semibold text-gray-700 mb-2",children:"Update Status:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:["pending","shortlisted","accepted","rejected"].map(oe=>s.jsxs("button",{onClick:()=>E(M._id,oe),disabled:v[M._id]||M.status.toLowerCase()===oe.toLowerCase(),className:`btn btn-sm capitalize 
                            ${M.status.toLowerCase()===oe.toLowerCase()?"btn-active "+T(oe).bgColor.replace("bg-","bg-opacity-50 border ")+T(oe).color:"btn-outline"}
                            ${T(oe).color.replace("border-","hover:bg-").replace("-500","-500 hover:text-white")}
                            disabled:opacity-50 disabled:cursor-not-allowed`,children:[v[M._id]&&M.status.toLowerCase()!==oe.toLowerCase()?s.jsx(ha,{className:"animate-spin mr-1"}):T(oe).icon,oe]},oe))})]})]},M._id)})})]})}):s.jsx("div",{className:"container mx-auto py-8 px-4 text-center",children:"Job details not found."})},$4=()=>{var v,N,S,H,b;const{candidateId:n}=Js(),[c,o]=R.useState(null),[u,f]=R.useState(!0),[m,h]=R.useState(null),g=R.useCallback(async()=>{var E,T;f(!0);try{const z=await Gs.getProfileById(n);o(z.data),h(null)}catch(z){console.error("Failed to fetch public profile:",z),h(((T=(E=z.response)==null?void 0:E.data)==null?void 0:T.message)||"Failed to load profile. It may not exist or is private.")}finally{f(!1)}},[n]);R.useEffect(()=>{g()},[g]);const y="bg-white p-6 rounded-lg shadow-md mb-6",x="mr-3 text-primary text-xl";return u?s.jsxs("div",{className:"flex justify-center items-center h-screen",children:[s.jsx(ha,{className:"animate-spin text-5xl text-primary"})," ",s.jsx("span",{className:"ml-4 text-2xl",children:"Loading Profile..."})]}):m?s.jsxs("div",{className:"container mx-auto py-12 px-4 text-center",children:[s.jsx(Eh,{className:"mx-auto text-6xl text-red-500 mb-4"}),s.jsx("h1",{className:"text-3xl font-semibold text-red-700",children:"Profile Not Found"}),s.jsx("p",{className:"text-gray-600 mt-2",children:m}),s.jsx(he,{to:"/jobs",className:"mt-6 inline-block btn btn-primary",children:"Back to Job Listings"})]}):c?s.jsx("div",{className:"bg-gray-100 min-h-screen py-12 px-4",children:s.jsxs("div",{className:"container mx-auto max-w-4xl",children:[s.jsxs("div",{className:"bg-gradient-to-r from-primary to-secondary text-white p-8 rounded-xl shadow-2xl mb-8 text-center md:text-left md:flex items-center",children:[(v=c.user)!=null&&v.avatarUrl?s.jsx("img",{src:c.user.avatarUrl,alt:c.fullName,className:"w-32 h-32 rounded-full mx-auto md:mx-0 md:mr-8 border-4 border-white shadow-lg"}):s.jsx(zh,{className:"w-32 h-32 text-blue-100 mx-auto md:mx-0 md:mr-8"}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-4xl font-bold mt-4 md:mt-0",children:c.fullName}),c.headline&&s.jsx("p",{className:"text-xl mt-1 text-blue-50",children:c.headline}),s.jsxs("div",{className:"mt-4 flex flex-wrap justify-center md:justify-start gap-4 text-sm",children:[c.contactNumber&&s.jsxs("span",{className:"flex items-center",children:[s.jsx(Th,{className:"mr-1.5"})," ",c.contactNumber]}),((N=c.user)==null?void 0:N.email)&&s.jsxs("span",{className:"flex items-center",children:[s.jsx(Is,{className:"mr-1.5"})," ",c.user.email]})]})]})]}),s.jsxs("div",{className:"md:grid md:grid-cols-3 md:gap-8",children:[s.jsxs("div",{className:"md:col-span-1 space-y-6 mb-8 md:mb-0",children:[c.resumeUrl&&s.jsx("div",{className:y,children:s.jsxs("a",{href:c.resumeUrl,target:"_blank",rel:"noopener noreferrer",className:"btn btn-primary w-full flex items-center justify-center text-lg",children:[s.jsx(j4,{className:"mr-2"})," Download Resume"]})}),s.jsxs("div",{className:y,children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Connect"}),s.jsxs("ul",{className:"space-y-3",children:[c.linkedinUrl&&s.jsx("li",{children:s.jsxs("a",{href:c.linkedinUrl,target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-blue-600 hover:underline",children:[s.jsx(jh,{className:x})," LinkedIn"]})}),c.githubUrl&&s.jsx("li",{children:s.jsxs("a",{href:c.githubUrl,target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-blue-600 hover:underline",children:[s.jsx(bh,{className:x})," GitHub"]})}),c.portfolioUrl&&s.jsx("li",{children:s.jsxs("a",{href:c.portfolioUrl,target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-blue-600 hover:underline",children:[s.jsx(N4,{className:x})," Portfolio"]})})]})]})]}),s.jsxs("div",{className:"md:col-span-2 space-y-8",children:[c.summary&&s.jsxs("div",{className:y,children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-700 mb-3",children:"Professional Summary"}),s.jsx("p",{className:"text-gray-600 whitespace-pre-wrap leading-relaxed",children:c.summary})]}),((S=c.experiences)==null?void 0:S.length)>0&&s.jsxs("div",{className:y,children:[s.jsxs("h2",{className:"text-2xl font-semibold text-gray-700 mb-5 flex items-center",children:[s.jsx(Ut,{className:x})," Work Experience"]}),s.jsx("div",{className:"space-y-6",children:c.experiences.map((E,T)=>s.jsxs("div",{className:"border-l-4 border-primary pl-4 py-2",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:E.title}),s.jsx("p",{className:"text-md font-medium text-secondary",children:E.company}),s.jsxs("p",{className:"text-sm text-gray-500 my-1",children:[E.startDate," - ",E.endDate||"Present"]}),s.jsx("p",{className:"text-gray-600 whitespace-pre-wrap text-sm",children:E.description})]},T))})]}),((H=c.educations)==null?void 0:H.length)>0&&s.jsxs("div",{className:y,children:[s.jsxs("h2",{className:"text-2xl font-semibold text-gray-700 mb-5 flex items-center",children:[s.jsx(S4,{className:x})," Education"]}),s.jsx("div",{className:"space-y-6",children:c.educations.map((E,T)=>s.jsxs("div",{className:"border-l-4 border-primary pl-4 py-2",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:E.degree}),s.jsx("p",{className:"text-md font-medium text-secondary",children:E.institution}),s.jsx("p",{className:"text-sm text-gray-500 my-1",children:E.fieldOfStudy}),s.jsxs("p",{className:"text-sm text-gray-500",children:[E.startDate," - ",E.endDate||"Present"]})]},T))})]}),((b=c.skills)==null?void 0:b.length)>0&&s.jsxs("div",{className:y,children:[s.jsxs("h2",{className:"text-2xl font-semibold text-gray-700 mb-4 flex items-center",children:[s.jsx(C4,{className:x})," Skills"]}),s.jsx("div",{className:"flex flex-wrap gap-3",children:c.skills.map((E,T)=>s.jsxs("span",{className:"bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium",children:[E.name," ",E.proficiency&&`(${E.proficiency})`]},T))})]})]})]})]})}):s.jsx("div",{className:"container mx-auto py-8 px-4 text-center",children:"Profile data is not available."})},P4=()=>s.jsx("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 p-6 text-center",children:s.jsxs("div",{className:"bg-white p-10 md:p-16 rounded-xl shadow-2xl max-w-lg w-full",children:[s.jsx(Eh,{className:"text-8xl text-yellow-400 mx-auto mb-6"}),s.jsx("h1",{className:"text-5xl md:text-6xl font-bold text-gray-800 mb-4",children:"404"}),s.jsx("h2",{className:"text-2xl md:text-3xl font-semibold text-gray-700 mb-6",children:"Page Not Found"}),s.jsx("p",{className:"text-gray-600 mb-8 text-lg",children:"Oops! The page you're looking for doesn't seem to exist. It might have been moved, deleted, or maybe you just mistyped the URL."}),s.jsxs(he,{to:"/",className:"btn btn-primary btn-lg inline-flex items-center group",children:[s.jsx(w4,{className:"mr-2 transition-transform duration-300 group-hover:scale-110"}),"Go to Homepage"]}),s.jsx("p",{className:"mt-10 text-sm text-gray-500",children:"If you believe this is an error, please contact support."})]})}),ca=({children:n,roles:c=[]})=>{const{user:o,loading:u}=wt(),f=Et();return u?s.jsx("div",{className:"flex justify-center items-center min-h-screen",children:s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})}):o?c.length>0&&!c.includes(o.role)?s.jsx("div",{className:"container mx-auto py-8 px-4 text-center",children:s.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:[s.jsx("h2",{className:"text-xl font-bold mb-2",children:"Access Denied"}),s.jsx("p",{children:"You don't have permission to access this page."}),s.jsxs("p",{className:"text-sm mt-2",children:["Required role: ",c.join(" or ")]})]})}):n:s.jsx(zu,{to:"/login",state:{from:f},replace:!0})};function W4(){const{user:n}=wt();return s.jsxs("div",{className:"flex flex-col min-h-screen",children:[s.jsx(M4,{}),s.jsx("main",{className:"flex-grow container mx-auto px-4 py-8",children:s.jsxs(ex,{children:[s.jsx(it,{path:"/",element:s.jsx(L4,{})}),s.jsx(it,{path:"/login",element:n?s.jsx(zu,{to:"/"}):s.jsx(H4,{})}),s.jsx(it,{path:"/register",element:n?s.jsx(zu,{to:"/"}):s.jsx(B4,{})}),s.jsx(it,{path:"/jobs",element:s.jsx(Y4,{})}),s.jsx(it,{path:"/jobs/:jobId",element:s.jsx(V4,{})}),s.jsx(it,{path:"/employer/dashboard",element:s.jsx(ca,{roles:["employer"],children:s.jsx(G4,{})})}),s.jsx(it,{path:"/employer/post-job",element:s.jsx(ca,{roles:["employer"],children:s.jsx(Q4,{})})}),s.jsx(it,{path:"/employer/job/:jobId/applications",element:s.jsx(ca,{roles:["employer"],children:s.jsx(F4,{})})}),s.jsx(it,{path:"/candidate/dashboard",element:s.jsx(ca,{roles:["candidate"],children:s.jsx(J4,{})})}),s.jsx(it,{path:"/candidate/profile",element:s.jsx(ca,{roles:["candidate"],children:s.jsx(k4,{})})}),s.jsx(it,{path:"/candidate/applications",element:s.jsx(ca,{roles:["candidate"],children:s.jsx(Z4,{})})}),s.jsx(it,{path:"/candidate/applications/:applicationId",element:s.jsx(ca,{roles:["candidate"],children:s.jsx(K4,{})})})," ",s.jsx(it,{path:"/candidate/:candidateId/profile",element:s.jsx($4,{})})," ",s.jsx(it,{path:"*",element:s.jsx(P4,{})})]})}),s.jsx(_4,{})]})}up.createRoot(document.getElementById("root")).render(s.jsx(Yl.StrictMode,{children:s.jsx(Ex,{children:s.jsx(m4,{children:s.jsx(W4,{})})})}));
