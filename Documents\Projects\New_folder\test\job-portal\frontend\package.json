{"name": "job-portal-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"axios": "^1.3.0", "jwt-decode": "^3.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^4.8.0", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.28.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "globals": "^16.2.0", "jsdom": "^26.1.0", "postcss": "^8.4.20", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vitest": "^3.1.4"}}