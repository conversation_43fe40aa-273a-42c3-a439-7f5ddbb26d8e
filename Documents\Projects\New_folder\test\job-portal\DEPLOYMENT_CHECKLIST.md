# 🚀 Job Portal Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Code Preparation
- [ ] All features tested locally
- [ ] Frontend builds successfully (`npm run build`)
- [ ] Backend starts without errors
- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] External APIs tested (if configured)

### ✅ Accounts Setup
- [ ] MongoDB Atlas account created
- [ ] Vercel account created
- [ ] Railway account created
- [ ] GitHub repository created and code pushed

## 🗄️ Database Setup (MongoDB Atlas)

### Step 1: Create Cluster
- [ ] Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
- [ ] Create free account
- [ ] Create new cluster (M0 Sandbox - Free)
- [ ] Choose cloud provider and region

### Step 2: Configure Access
- [ ] Create database user:
  - Username: `jobportal-user`
  - Password: Generate strong password
  - Role: `Atlas admin` or `Read and write to any database`
- [ ] Add IP addresses to whitelist:
  - Add `0.0.0.0/0` for all IPs (or specific IPs for security)

### Step 3: Get Connection String
- [ ] Click "Connect" on your cluster
- [ ] Choose "Connect your application"
- [ ] Copy connection string
- [ ] Replace `<password>` with your actual password
- [ ] Save connection string for backend deployment

## 🖥️ Backend Deployment (Railway)

### Step 1: Prepare Backend
- [ ] Ensure `package.json` has correct start script
- [ ] Verify all dependencies are listed
- [ ] Test backend locally one more time

### Step 2: Deploy to Railway

#### Option A: Railway CLI
- [ ] Install Railway CLI: `npm install -g @railway/cli`
- [ ] Login: `railway login`
- [ ] Navigate to backend folder: `cd backend`
- [ ] Initialize: `railway init`
- [ ] Deploy: `railway up`

#### Option B: Railway Dashboard
- [ ] Go to [Railway](https://railway.app)
- [ ] Click "New Project"
- [ ] Choose "Deploy from GitHub repo"
- [ ] Select your repository
- [ ] Set root directory to `backend`

### Step 3: Configure Environment Variables
Set these in Railway dashboard or CLI:
- [ ] `MONGODB_URI` = Your MongoDB Atlas connection string
- [ ] `JWT_SECRET` = Strong secret key (32+ characters)
- [ ] `NODE_ENV` = `production`
- [ ] `PORT` = `5000`
- [ ] `RAPIDAPI_KEY` = Your RapidAPI key (optional)
- [ ] `ADZUNA_APP_ID` = Your Adzuna app ID (optional)
- [ ] `ADZUNA_APP_KEY` = Your Adzuna app key (optional)

### Step 4: Test Backend
- [ ] Visit your Railway app URL
- [ ] Test API endpoint: `https://your-app.railway.app/api/jobs`
- [ ] Check logs for any errors

## 🌐 Frontend Deployment (Vercel)

### Step 1: Prepare Frontend
- [ ] Update API URL in environment variables
- [ ] Test build locally: `npm run build`
- [ ] Verify all routes work in production build

### Step 2: Deploy to Vercel

#### Option A: Vercel CLI
- [ ] Install Vercel CLI: `npm install -g vercel`
- [ ] Navigate to project root: `cd job-portal`
- [ ] Run: `vercel`
- [ ] Follow prompts:
  - Project name: `job-portal`
  - Directory: `frontend`
  - Build command: `npm run build`
  - Output directory: `dist`

#### Option B: Vercel Dashboard
- [ ] Go to [Vercel](https://vercel.com)
- [ ] Click "New Project"
- [ ] Import your GitHub repository
- [ ] Set framework preset to "Vite"
- [ ] Set root directory to `frontend`
- [ ] Deploy

### Step 3: Configure Environment Variables
Set these in Vercel dashboard:
- [ ] `VITE_API_URL` = Your Railway backend URL + `/api`
  - Example: `https://your-app.railway.app/api`
- [ ] `VITE_APP_NAME` = `Job Portal`
- [ ] `VITE_ENABLE_EXTERNAL_JOBS` = `true`

### Step 4: Test Frontend
- [ ] Visit your Vercel app URL
- [ ] Test user registration
- [ ] Test user login
- [ ] Test job posting (as employer)
- [ ] Test job application (as candidate)
- [ ] Test external jobs (if configured)

## 🔧 Post-Deployment Configuration

### Backend Updates
- [ ] Update CORS settings to include your Vercel domain
- [ ] Test all API endpoints
- [ ] Monitor logs for errors

### Frontend Updates
- [ ] Verify API calls are working
- [ ] Test all user flows
- [ ] Check browser console for errors

## 🧪 Final Testing

### User Flows
- [ ] **Registration**: Create candidate and employer accounts
- [ ] **Login**: Test authentication
- [ ] **Job Posting**: Employer can post jobs
- [ ] **Job Search**: Candidates can search and view jobs
- [ ] **Applications**: Candidates can apply, employers can view applications
- [ ] **External Jobs**: External job integration works (if configured)

### Technical Tests
- [ ] All API endpoints respond correctly
- [ ] Database operations work
- [ ] File uploads work (if implemented)
- [ ] Email notifications work (if implemented)
- [ ] Error handling works properly

## 🎉 Deployment Complete!

### Your Live URLs
- **Frontend**: `https://your-project.vercel.app`
- **Backend**: `https://your-project.railway.app`

### Next Steps
- [ ] Set up custom domain (optional)
- [ ] Configure monitoring and alerts
- [ ] Set up analytics
- [ ] Plan regular backups
- [ ] Document API for future development

## 🚨 Troubleshooting

### Common Issues
1. **CORS Errors**: Update backend CORS settings
2. **API Not Found**: Check VITE_API_URL environment variable
3. **Database Connection**: Verify MongoDB Atlas connection string
4. **Build Failures**: Check Node.js version and dependencies

### Getting Help
- Railway logs: Check Railway dashboard
- Vercel logs: Check Vercel dashboard
- Browser console: Check for frontend errors
- Network tab: Check API call responses

## 📞 Support Resources
- [Railway Documentation](https://docs.railway.app)
- [Vercel Documentation](https://vercel.com/docs)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com)
- [Vite Documentation](https://vitejs.dev/guide)
- [Express.js Documentation](https://expressjs.com)
