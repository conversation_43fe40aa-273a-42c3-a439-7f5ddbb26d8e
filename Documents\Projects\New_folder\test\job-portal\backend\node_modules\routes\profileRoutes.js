const express = require('express');
const router = express.Router();
const {
    createOrUpdateProfile,
    getCurrentCandidateProfile,
    getProfileByUserId, // For employers or public view if allowed
    getAllProfiles, // For admin or employer search (with filters)
    deleteProfile // Candidate deletes their own profile
} = require('../controllers/profileController');
const { protect, authorize, candidate } = require('../middleware/authMiddleware');

// @route   POST /api/profiles
// @desc    Create or update current candidate's profile
// @access  Private/Candidate
router.post('/', protect, authorize('candidate'), createOrUpdateProfile); // or protect, candidate

// @route   GET /api/profiles/me
// @desc    Get current logged-in candidate's profile
// @access  Private/Candidate
router.get('/me', protect, authorize('candidate'), getCurrentCandidateProfile);

// @route   GET /api/profiles/user/:userId
// @desc    Get profile by user ID (e.g., for an employer to view a candidate's profile)
// @access  Private (Employers might need access, or public if profiles are searchable)
// Add specific authorization if needed, e.g. authorize('employer') or a more complex check
router.get('/user/:userId', protect, getProfileByUserId); 

// @route   GET /api/profiles
// @desc    Get all candidate profiles (e.g., for employers to search, with pagination/filters)
// @access  Private/Employer (or Admin)
router.get('/', protect, authorize('employer'), getAllProfiles);

// @route   DELETE /api/profiles
// @desc    Delete current candidate's profile and associated user account (or just profile)
// @access  Private/Candidate
router.delete('/', protect, authorize('candidate'), deleteProfile);

module.exports = router;