const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
    jobId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Job',
        required: true
    },
    candidateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // User with role 'candidate'
        required: true
    },
    employerId: { // Denormalized for quick filtering by employer
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // User with role 'employer'
        required: true
    },
    status: {
        type: String,
        enum: ['Applied', 'Viewed', 'Shortlisted', 'Interviewing', 'Offered', 'Rejected', 'Withdrawn'],
        default: 'Applied'
    },
    applicationDate: {
        type: Date,
        default: Date.now
    },
    coverLetter: {
        type: String,
        trim: true,
        maxlength: [5000, 'Cover letter cannot exceed 5000 characters']
    },
    // Optional: Snapshot of key profile info at the time of application for historical record
    // This can be useful if the candidate's profile changes later
    profileSnapshot: {
        name: String,
        email: String,
        skills: [String],
        headline: String,
        resumeUrl: String // If a specific resume was uploaded/linked for this application
    },
    notes: [{ // Notes by employer or candidate regarding this application
        byUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
        note: String,
        date: { type: Date, default: Date.now }
    }],
    assignmentSubmission: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AssignmentSubmission',
        default: null
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Middleware to update `updatedAt` field on save
applicationSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    // If it's a new application and job model has applicationsCount, consider incrementing it here or via a post-save hook / controller logic
    next();
});

// Ensure a candidate can apply only once to a specific job
applicationSchema.index({ jobId: 1, candidateId: 1 }, { unique: true });

// Other useful indexes
applicationSchema.index({ jobId: 1 });
applicationSchema.index({ candidateId: 1 });
applicationSchema.index({ employerId: 1 });
applicationSchema.index({ status: 1 });
applicationSchema.index({ applicationDate: -1 });

module.exports = mongoose.model('Application', applicationSchema);