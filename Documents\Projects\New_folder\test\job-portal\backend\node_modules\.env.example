# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/job_portal

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# External Job APIs Configuration (Optional)
# Get your RapidAPI key from https://rapidapi.com/letscrape-6bRBa3QguO5/api/jsearch
RAPIDAPI_KEY=your_rapidapi_key_here

# Adzuna API Configuration (Optional)
# Get your credentials from https://developer.adzuna.com/
ADZUNA_APP_ID=your_adzuna_app_id_here
ADZUNA_APP_KEY=your_adzuna_app_key_here

# External Jobs Settings
EXTERNAL_JOBS_ENABLED=true
EXTERNAL_JOBS_CACHE_DURATION=1800000
EXTERNAL_JOBS_SYNC_INTERVAL=3600000

# Email Configuration (Optional - for future features)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# File Upload Configuration (Optional - for future features)
# CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
# CLOUDINARY_API_KEY=your_cloudinary_api_key
# CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Redis Configuration (Optional - for production caching)
# REDIS_URL=redis://localhost:6379

# Rate Limiting (Optional)
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
# LOG_LEVEL=info
# LOG_FILE=logs/app.log
