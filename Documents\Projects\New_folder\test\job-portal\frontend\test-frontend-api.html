<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
</head>
<body>
    <h1>Frontend API Test</h1>
    <button onclick="testAPI()">Test Jobs API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing API...';
            
            try {
                const response = await fetch('http://localhost:5000/api/jobs?pageSize=6&sortBy=postedDate:desc');
                const data = await response.json();
                
                console.log('API Response:', data);
                
                if (data.jobs && data.jobs.length > 0) {
                    resultDiv.innerHTML = `
                        <h2>✅ Success!</h2>
                        <p>Found ${data.jobs.length} jobs</p>
                        <h3>Jobs:</h3>
                        <ul>
                            ${data.jobs.map(job => `<li>${job.title} at ${job.companyName}</li>`).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = '<h2>❌ No jobs found</h2>';
                }
            } catch (error) {
                console.error('API Error:', error);
                resultDiv.innerHTML = `<h2>❌ Error</h2><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
