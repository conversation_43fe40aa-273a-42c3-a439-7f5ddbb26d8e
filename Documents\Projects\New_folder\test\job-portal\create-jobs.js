const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Sample jobs
const jobs = [
  {
    title: 'Senior Frontend Developer',
    description: 'We are looking for an experienced frontend developer to join our team. You will work on cutting-edge React applications and collaborate with our design team to create amazing user experiences.',
    location: 'San Francisco, CA',
    requirements: ['React', 'JavaScript', 'CSS', 'HTML', '3+ years experience'],
    skills: ['react', 'javascript', 'css', 'html'],
    salaryMin: 80000,
    salaryMax: 120000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  },
  {
    title: 'Backend Developer',
    description: 'Join our backend team to build scalable APIs and services. You will work with Node.js, MongoDB, and modern cloud technologies to create robust backend systems.',
    location: 'New York, NY',
    requirements: ['Node.js', 'MongoDB', 'Express', 'REST APIs', '2+ years experience'],
    skills: ['nodejs', 'mongodb', 'express', 'api'],
    salaryMin: 75000,
    salaryMax: 110000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000)
  },
  {
    title: 'Full Stack Developer',
    description: 'Work on both frontend and backend technologies in a fast-paced startup environment. You will have the opportunity to work with the latest technologies and make a real impact.',
    location: 'Austin, TX',
    requirements: ['React', 'Node.js', 'MongoDB', 'JavaScript', '2+ years experience'],
    skills: ['react', 'nodejs', 'mongodb', 'javascript'],
    salaryMin: 70000,
    salaryMax: 100000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000)
  },
  {
    title: 'DevOps Engineer',
    description: 'Help us build and maintain our cloud infrastructure. You will work with AWS, Docker, Kubernetes, and CI/CD pipelines.',
    location: 'Remote',
    requirements: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', '3+ years experience'],
    skills: ['aws', 'docker', 'kubernetes', 'devops'],
    salaryMin: 85000,
    salaryMax: 130000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000)
  },
  {
    title: 'UI/UX Designer',
    description: 'Create beautiful and intuitive user interfaces for our web and mobile applications. You will work closely with our development team.',
    location: 'Los Angeles, CA',
    requirements: ['Figma', 'Adobe Creative Suite', 'User Research', '2+ years experience'],
    skills: ['figma', 'design', 'ux', 'ui'],
    salaryMin: 65000,
    salaryMax: 95000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000)
  },
  {
    title: 'Data Scientist',
    description: 'Analyze large datasets to extract meaningful insights and build predictive models. You will work with Python, R, and machine learning frameworks.',
    location: 'Seattle, WA',
    requirements: ['Python', 'R', 'Machine Learning', 'Statistics', '3+ years experience'],
    skills: ['python', 'r', 'machinelearning', 'statistics'],
    salaryMin: 90000,
    salaryMax: 140000,
    jobType: 'Full-time',
    applicationDeadline: new Date(Date.now() + 40 * 24 * 60 * 60 * 1000)
  }
];

async function createJobs() {
  try {
    console.log('Logging in as employers and creating jobs...');
    
    // Login as first employer
    const employer1Response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const employer1Token = employer1Response.data.token;
    console.log('✓ Logged in as Tech Corp employer');
    
    // Login as second employer
    const employer2Response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const employer2Token = employer2Response.data.token;
    console.log('✓ Logged in as StartupXYZ employer');
    
    // Create jobs alternating between employers
    for (let i = 0; i < jobs.length; i++) {
      const job = jobs[i];
      const token = i % 2 === 0 ? employer1Token : employer2Token;
      const companyName = i % 2 === 0 ? 'Tech Corp' : 'StartupXYZ';
      
      try {
        const response = await axios.post(`${API_BASE}/jobs`, job, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        console.log(`✓ Created job: ${job.title} at ${companyName}`);
      } catch (error) {
        console.log(`✗ Failed to create job ${job.title}:`, error.response?.data?.message || error.message);
        if (error.response?.data) {
          console.log('Error details:', error.response.data);
        }
      }
    }
    
    console.log('\n✅ Job creation completed!');
    console.log('You can now view jobs on the homepage.');
    
  } catch (error) {
    console.error('Error creating jobs:', error.response?.data || error.message);
  }
}

createJobs();
