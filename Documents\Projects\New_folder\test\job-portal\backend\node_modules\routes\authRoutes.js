const express = require('express');
const router = express.Router();
const {
    registerUser,
    loginUser,
    getMe,
    logoutUser, // Placeholder, JWT logout is client-side or via token blocklist
    updatePassword,
    forgotPassword,
    resetPassword
} = require('../controllers/authController');
const { protect } = require('../middleware/authMiddleware');

// @route   POST /api/auth/register
// @desc    Register a new user (candidate or employer)
// @access  Public
router.post('/register', registerUser);

// @route   POST /api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post('/login', loginUser);

// @route   GET /api/auth/me
// @desc    Get current logged-in user details
// @access  Private
router.get('/me', protect, getMe);

// @route   POST /api/auth/logout
// @desc    Logout user (typically handled client-side for JWT)
// @access  Private
router.post('/logout', protect, logoutUser);

// @route   PUT /api/auth/updatepassword
// @desc    Update user password
// @access  Private
router.put('/updatepassword', protect, updatePassword);

// @route   POST /api/auth/forgotpassword
// @desc    Request password reset (e.g., send email with token)
// @access  Public
router.post('/forgotpassword', forgotPassword);

// @route   PUT /api/auth/resetpassword/:resettoken
// @desc    Reset password using token
// @access  Public
router.put('/resetpassword/:resettoken', resetPassword);

module.exports = router;