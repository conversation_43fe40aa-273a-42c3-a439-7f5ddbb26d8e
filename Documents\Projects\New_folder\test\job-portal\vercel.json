{"version": 2, "name": "job-portal", "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "https://your-backend-url.com/api/$1"}, {"src": "/(.*)", "dest": "frontend/$1"}], "env": {"VITE_API_URL": "@vite_api_url"}, "build": {"env": {"VITE_API_URL": "@vite_api_url"}}, "functions": {"frontend/dist/**": {"includeFiles": "frontend/dist/**"}}, "rewrites": [{"source": "/api/(.*)", "destination": "https://your-backend-url.com/api/$1"}, {"source": "/((?!api).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Authorization"}]}]}