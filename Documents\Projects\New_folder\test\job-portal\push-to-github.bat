@echo off
echo 🚀 Job Portal - Push to GitHub
echo.

echo 📋 Step 1: Initializing Git Repository...
git init
if %errorlevel% neq 0 (
    echo ❌ Git init failed! Make sure Git is installed.
    pause
    exit /b 1
)

echo ✅ Git initialized successfully!
echo.

echo 📋 Step 2: Adding all files...
git add .
if %errorlevel% neq 0 (
    echo ❌ Git add failed!
    pause
    exit /b 1
)

echo ✅ Files added successfully!
echo.

echo 📋 Step 3: Creating initial commit...
git commit -m "Initial commit - Job Portal ready for deployment"
if %errorlevel% neq 0 (
    echo ❌ Git commit failed!
    pause
    exit /b 1
)

echo ✅ Initial commit created successfully!
echo.

echo 🎉 Git repository is ready!
echo.
echo 📝 Next steps:
echo    1. Go to GitHub.com
echo    2. Create a new repository named 'job-portal'
echo    3. Copy the remote add command from GitHub
echo    4. Run it in this directory
echo    5. Run: git push -u origin main
echo.
echo Example commands you'll need to run:
echo    git remote add origin https://github.com/YOUR_USERNAME/job-portal.git
echo    git branch -M main
echo    git push -u origin main
echo.

pause
